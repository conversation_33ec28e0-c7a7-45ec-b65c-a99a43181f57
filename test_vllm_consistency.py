#!/usr/bin/env python3
"""
测试VLLM情感分析生成器与原有llm_response_generator.py的一致性
主要验证：
1. 循环逻辑（seed，prompt选取，重复次数）
2. 保存方式（MongoDB文档结构）
"""

import os
import sys
import logging
from typing import Dict, Any, List
import yaml

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_test_config() -> Dict[str, Any]:
    """加载测试配置"""
    return {
        'model': {
            'name': 'Qwen/Qwen3-14B-FP8',
            'base_url': 'http://localhost:8000',
            'api_key': 'token-abc123',
            'temperature': 0.7,
            'top_p': 0.95,
            'max_tokens': 1500,
            'enable_thinking': True,
            'enable_logprobs': True,
            'top_logprobs': 1,
            'stream': False
        },
        'output': {
            'format': 'mongodb',
            'mongo': {
                'host': 'localhost',
                'port': 27017,
                'database': 'LLM-UQ-TEST',
                'collection': 'test_response_collection'
            }
        },
        'tasks': {
            'sentiment_analysis': {
                'enabled': True,
                'name': 'sentiment_analysis',
                'task_category': 'sentiment_analysis',
                'dataset_source': 'twitter_sentiment',
                'sample_prompts': 3,  # 减少到3个prompt用于测试
                'attempts_per_prompt': 2,  # 减少到2次尝试用于测试
                'template_variable': 'tweet',
                'id_field': 'id',
                'text_field': 'text',
                'label_field': 'label',
                'data_file': 'sampled_semeval.csv',
                'max_samples': 5  # 只处理5个样本用于测试
            }
        }
    }

def create_test_data() -> List[Dict[str, Any]]:
    """创建测试数据"""
    return [
        {'id': 'test_001', 'text': 'I love this movie!', 'label': 'Positive'},
        {'id': 'test_002', 'text': 'This is terrible.', 'label': 'Negative'},
        {'id': 'test_003', 'text': 'It is okay.', 'label': 'Neutral'},
        {'id': 'test_004', 'text': 'Amazing performance!', 'label': 'Positive'},
        {'id': 'test_005', 'text': 'Not good at all.', 'label': 'Negative'}
    ]

def test_prompt_selection_consistency():
    """测试prompt选择的一致性"""
    logger.info("=== 测试Prompt选择一致性 ===")
    
    try:
        from vllm_sentiment_generator import VLLMSentimentGenerator
        
        # 创建生成器
        generator = VLLMSentimentGenerator(config_path=None)
        generator.config = load_test_config()
        
        # 测试相同种子是否产生相同的prompt选择
        seed = 42
        sample_count = 3
        
        prompts1 = generator.get_sentiment_prompts(sample_count, seed=seed)
        prompts2 = generator.get_sentiment_prompts(sample_count, seed=seed)
        
        # 验证一致性
        if len(prompts1) == len(prompts2):
            logger.info(f"✓ Prompt数量一致: {len(prompts1)}")
        else:
            logger.error(f"✗ Prompt数量不一致: {len(prompts1)} vs {len(prompts2)}")
            return False
        
        # 验证prompt ID一致性
        ids1 = [p.get('id', 'unknown') for p in prompts1]
        ids2 = [p.get('id', 'unknown') for p in prompts2]
        
        if ids1 == ids2:
            logger.info(f"✓ Prompt ID序列一致: {ids1}")
        else:
            logger.error(f"✗ Prompt ID序列不一致: {ids1} vs {ids2}")
            return False
        
        logger.info("✓ Prompt选择一致性测试通过")
        return True
        
    except Exception as e:
        logger.error(f"✗ Prompt选择一致性测试失败: {e}")
        return False

def test_loop_logic():
    """测试循环逻辑"""
    logger.info("=== 测试循环逻辑 ===")
    
    try:
        from vllm_sentiment_generator import VLLMSentimentGenerator
        
        # 创建生成器（不连接VLLM服务，只测试逻辑）
        config = load_test_config()
        generator = VLLMSentimentGenerator(config_path=None)
        generator.config = config
        
        # 创建测试数据
        test_data = create_test_data()
        
        # 验证循环逻辑参数
        task_config = config.get('tasks', {}).get('sentiment_analysis', {})
        sample_prompts = task_config.get('sample_prompts', 5)
        attempts_per_prompt = task_config.get('attempts_per_prompt', 6)
        
        logger.info(f"配置参数:")
        logger.info(f"  - 数据项数量: {len(test_data)}")
        logger.info(f"  - 每项prompt数量: {sample_prompts}")
        logger.info(f"  - 每prompt尝试次数: {attempts_per_prompt}")
        logger.info(f"  - 预期总调用次数: {len(test_data) * sample_prompts * attempts_per_prompt}")
        
        # 测试每个数据项的prompt选择
        for item_idx, item in enumerate(test_data):
            logger.info(f"数据项 {item_idx}: {item['id']}")
            
            # 使用item_idx作为种子选择prompts
            selected_prompts = generator.get_sentiment_prompts(sample_prompts, seed=item_idx)
            
            logger.info(f"  选中的prompts: {[p.get('id', 'unknown') for p in selected_prompts]}")
            
            # 验证prompt数量
            if len(selected_prompts) != sample_prompts:
                logger.error(f"  ✗ Prompt数量不正确: 期望{sample_prompts}, 实际{len(selected_prompts)}")
                return False
        
        logger.info("✓ 循环逻辑测试通过")
        return True
        
    except Exception as e:
        logger.error(f"✗ 循环逻辑测试失败: {e}")
        return False

def test_document_structure():
    """测试MongoDB文档结构"""
    logger.info("=== 测试MongoDB文档结构 ===")
    
    try:
        from vllm_sentiment_generator import VLLMSentimentGenerator
        from datetime import datetime, timezone
        
        # 创建生成器
        generator = VLLMSentimentGenerator(config_path=None)
        generator.config = load_test_config()
        
        # 模拟结果数据
        mock_result = {
            'predicted_sentiment': 'Positive',
            'confidence': 0.85,
            'total_logprob': -2.5,
            'sentiment_probabilities': {'positive': 0.85, 'negative': 0.10, 'neutral': 0.05},
            'raw_response': 'I think this is positive. [Label]: Positive',
            'thinking_content': 'Let me analyze this...',
            'actual_response': '[Label]: Positive',
            'raw_answer': '[Label]: Positive',
            'model': 'Qwen/Qwen3-14B-FP8',
            'finish_reason': 'stop',
            'prompt_raw_text': 'Analyze sentiment: I love this movie!',
            'logprobs': {'content': []}
        }
        
        # 模拟记录数据
        mock_record = {'id': 'test_001', 'text': 'I love this movie!', 'label': 'Positive'}
        
        # 构建文档
        document = generator._build_mongodb_document(
            result=mock_result,
            record=mock_record,
            item_idx=0,
            prompt_dict={'id': 'sentiment_01'},
            prompt_number=1,
            attempt=1,
            run_id='test-run-123',
            task_name='sentiment_analysis',
            dataset_source='twitter_sentiment',
            task_config=generator.config.get('tasks', {}).get('sentiment_analysis', {})
        )
        
        # 验证必需字段
        required_fields = [
            'run_id', 'task_id', 'task_name', 'dataset_source', 'task_category',
            'input_text', 'reference_answer', 'model_identifier', 'prompt_variant',
            'prompt_seed', 'prompt_index', 'prompt_raw_text', 'generation_config',
            'task_attempt_prompt', 'raw_response', 'thinking_content', 'actual_response',
            'response_logprobs', 'finish_reason', 'raw_answer', 'parsed_answer',
            'execution_timestamp'
        ]
        
        missing_fields = []
        for field in required_fields:
            if field not in document:
                missing_fields.append(field)
        
        if missing_fields:
            logger.error(f"✗ 缺少必需字段: {missing_fields}")
            return False
        
        # 验证关键字段值
        if document['prompt_seed'] != 0:
            logger.error(f"✗ prompt_seed不正确: 期望0, 实际{document['prompt_seed']}")
            return False
        
        if document['prompt_index'] != 1:
            logger.error(f"✗ prompt_index不正确: 期望1, 实际{document['prompt_index']}")
            return False
        
        if document['task_attempt_prompt'] != 1:
            logger.error(f"✗ task_attempt_prompt不正确: 期望1, 实际{document['task_attempt_prompt']}")
            return False
        
        logger.info("✓ MongoDB文档结构测试通过")
        logger.info(f"  文档包含 {len(document)} 个字段")
        logger.info(f"  关键字段值正确: prompt_seed={document['prompt_seed']}, prompt_index={document['prompt_index']}, task_attempt_prompt={document['task_attempt_prompt']}")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ MongoDB文档结构测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("开始VLLM情感分析生成器一致性测试")
    
    tests = [
        ("Prompt选择一致性", test_prompt_selection_consistency),
        ("循环逻辑", test_loop_logic),
        ("MongoDB文档结构", test_document_structure)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"运行测试: {test_name}")
        logger.info(f"{'='*50}")
        
        if test_func():
            passed += 1
            logger.info(f"✓ {test_name} 测试通过")
        else:
            logger.error(f"✗ {test_name} 测试失败")
    
    logger.info(f"\n{'='*50}")
    logger.info(f"测试总结: {passed}/{total} 测试通过")
    logger.info(f"{'='*50}")
    
    if passed == total:
        logger.info("🎉 所有测试通过！VLLM生成器与原有逻辑一致。")
        return 0
    else:
        logger.error("❌ 部分测试失败，需要修复不一致的地方。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
