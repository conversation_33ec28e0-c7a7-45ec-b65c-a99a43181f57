#!/usr/bin/env python3
"""
对combined_uq_results.csv中的UQ数据进行全局归一化处理
为每个UQ方法计算全局Min-Max归一化，并添加归一化后的列到CSV中
"""

import pandas as pd
import numpy as np
from pathlib import Path
import argparse

def safe_float(v):
    """安全转换为浮点数"""
    try:
        if pd.isna(v):
            return np.nan
        return float(v)
    except Exception:
        return np.nan

def compute_global_normalization_params(df: pd.DataFrame) -> dict:
    """计算每个UQ方法的全局归一化参数"""
    print("计算全局归一化参数...")
    global_params = {}
    
    # 获取所有唯一的UQ方法
    uq_methods = df['uq_method'].unique()
    print(f"发现 {len(uq_methods)} 个UQ方法")
    
    for method in uq_methods:
        # 提取该UQ方法的所有数据
        method_data = df[df['uq_method'] == method].copy()
        
        # 获取UQ分数 (优先使用uncertainty_score，其次uq_value)
        uq_scores = []
        for _, row in method_data.iterrows():
            score = safe_float(row.get('uncertainty_score'))
            if pd.isna(score):
                score = safe_float(row.get('uq_value'))
            if not pd.isna(score):
                uq_scores.append(score)
        
        if len(uq_scores) > 0:
            uq_scores = np.array(uq_scores)
            global_min = float(np.min(uq_scores))
            global_max = float(np.max(uq_scores))
            global_mean = float(np.mean(uq_scores))
            global_std = float(np.std(uq_scores))
            global_median = float(np.median(uq_scores))
            global_q25 = float(np.percentile(uq_scores, 25))
            global_q75 = float(np.percentile(uq_scores, 75))
            global_iqr = global_q75 - global_q25
            global_range = global_max - global_min
            global_l2_norm = float(np.linalg.norm(uq_scores))

            global_params[method] = {
                'min': global_min,
                'max': global_max,
                'mean': global_mean,
                'std': global_std,
                'median': global_median,
                'q25': global_q25,
                'q75': global_q75,
                'iqr': global_iqr,
                'range': global_range,
                'l2_norm': global_l2_norm,
                'count': len(uq_scores)
            }
            
            print(f"  {method}:")
            print(f"    范围: [{global_min:.4f}, {global_max:.4f}] (range={global_max-global_min:.4f})")
            print(f"    均值±标准差: {global_mean:.4f}±{global_std:.4f}")
            print(f"    样本数: {len(uq_scores)}")
        else:
            print(f"  {method}: 无有效数据")
    
    return global_params

def apply_global_normalization(df: pd.DataFrame, global_params: dict) -> pd.DataFrame:
    """应用全局归一化到数据框"""
    print("\n应用全局归一化...")
    df_normalized = df.copy()

    # 添加多种归一化列
    df_normalized['uq_score_minmax_global'] = np.nan      # Min-Max归一化到[0,1]
    df_normalized['uq_score_zscore_global'] = np.nan      # Z-score标准化
    df_normalized['uq_score_robust_global'] = np.nan      # Robust归一化(使用中位数和IQR)
    df_normalized['uq_score_maxnorm_global'] = np.nan     # 除以最大值归一化
    df_normalized['uq_score_unitvector_global'] = np.nan  # 单位向量归一化

    # 添加归一化参数信息列
    df_normalized['global_min'] = np.nan
    df_normalized['global_max'] = np.nan
    df_normalized['global_mean'] = np.nan
    df_normalized['global_std'] = np.nan
    df_normalized['global_median'] = np.nan
    df_normalized['global_q25'] = np.nan
    df_normalized['global_q75'] = np.nan
    df_normalized['global_iqr'] = np.nan
    df_normalized['global_range'] = np.nan
    df_normalized['normalization_applied'] = False
    
    for method in global_params:
        method_mask = df_normalized['uq_method'] == method
        method_data = df_normalized[method_mask].copy()
        
        if len(method_data) == 0:
            continue
            
        params = global_params[method]
        global_min = params['min']
        global_max = params['max']
        global_mean = params['mean']
        global_std = params['std']
        global_median = params['median']
        global_q25 = params['q25']
        global_q75 = params['q75']
        global_iqr = params['iqr']
        global_range = params['range']
        global_l2_norm = params['l2_norm']

        # 获取原始UQ分数
        original_scores = []
        for idx, row in method_data.iterrows():
            score = safe_float(row.get('uncertainty_score'))
            if pd.isna(score):
                score = safe_float(row.get('uq_value'))
            original_scores.append(score)

        original_scores = np.array(original_scores)
        method_indices = method_data.index

        # 1. Min-Max归一化到[0,1]
        if global_range > 0:
            minmax_scores = (original_scores - global_min) / global_range
        else:
            minmax_scores = np.full_like(original_scores, 0.5)

        # 2. Z-score标准化
        if global_std > 0:
            zscore_scores = (original_scores - global_mean) / global_std
        else:
            zscore_scores = np.zeros_like(original_scores)

        # 3. Robust归一化 (使用中位数和IQR)
        if global_iqr > 0:
            robust_scores = (original_scores - global_median) / global_iqr
        else:
            robust_scores = np.zeros_like(original_scores)

        # 4. Max归一化 (除以最大值)
        if global_max > 0:
            maxnorm_scores = original_scores / global_max
        else:
            maxnorm_scores = np.zeros_like(original_scores)

        # 5. 单位向量归一化 (除以L2范数)
        if global_l2_norm > 0:
            unitvector_scores = original_scores / global_l2_norm
        else:
            unitvector_scores = np.zeros_like(original_scores)

        # 更新数据框 - 添加所有归一化结果
        df_normalized.loc[method_indices, 'uq_score_minmax_global'] = minmax_scores
        df_normalized.loc[method_indices, 'uq_score_zscore_global'] = zscore_scores
        df_normalized.loc[method_indices, 'uq_score_robust_global'] = robust_scores
        df_normalized.loc[method_indices, 'uq_score_maxnorm_global'] = maxnorm_scores
        df_normalized.loc[method_indices, 'uq_score_unitvector_global'] = unitvector_scores

        # 添加全局统计参数
        df_normalized.loc[method_indices, 'global_min'] = global_min
        df_normalized.loc[method_indices, 'global_max'] = global_max
        df_normalized.loc[method_indices, 'global_mean'] = global_mean
        df_normalized.loc[method_indices, 'global_std'] = global_std
        df_normalized.loc[method_indices, 'global_median'] = global_median
        df_normalized.loc[method_indices, 'global_q25'] = global_q25
        df_normalized.loc[method_indices, 'global_q75'] = global_q75
        df_normalized.loc[method_indices, 'global_iqr'] = global_iqr
        df_normalized.loc[method_indices, 'global_range'] = global_range
        df_normalized.loc[method_indices, 'normalization_applied'] = True
        
        print(f"  {method}: 归一化 {len(method_data)} 条记录")
    
    return df_normalized

def generate_normalization_summary(global_params: dict, output_dir: Path):
    """生成归一化参数摘要文件"""
    summary_data = []

    for method, params in global_params.items():
        summary_data.append({
            'uq_method': method,
            'global_min': params['min'],
            'global_max': params['max'],
            'global_mean': params['mean'],
            'global_std': params['std'],
            'global_median': params['median'],
            'global_q25': params['q25'],
            'global_q75': params['q75'],
            'global_iqr': params['iqr'],
            'global_range': params['range'],
            'global_l2_norm': params['l2_norm'],
            'sample_count': params['count'],
            'coefficient_of_variation': params['std'] / params['mean'] if params['mean'] != 0 else np.inf
        })

    summary_df = pd.DataFrame(summary_data)
    summary_df = summary_df.sort_values('uq_method')

    summary_file = output_dir / 'global_normalization_summary.csv'
    summary_df.to_csv(summary_file, index=False)
    print(f"\n归一化参数摘要保存到: {summary_file}")

    return summary_df

def main():
    parser = argparse.ArgumentParser(description='对UQ数据进行全局归一化处理')
    parser.add_argument('--input', default='uq_result_analysis/data/combined_uq_results.csv', 
                       help='输入CSV文件路径')
    parser.add_argument('--output', default='uq_result_analysis/data/combined_uq_results_normalized.csv',
                       help='输出CSV文件路径')
    parser.add_argument('--summary-dir', default='uq_result_analysis/data',
                       help='归一化摘要文件输出目录')
    args = parser.parse_args()
    
    # 检查输入文件
    input_path = Path(args.input)
    if not input_path.exists():
        print(f"错误: 输入文件不存在: {input_path}")
        return
    
    # 创建输出目录
    output_path = Path(args.output)
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    summary_dir = Path(args.summary_dir)
    summary_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"读取数据文件: {input_path}")
    df = pd.read_csv(input_path)
    print(f"原始数据: {len(df)} 行, {len(df.columns)} 列")
    
    # 只处理成功的记录
    success_df = df[df['status'] == 'success'].copy()
    print(f"成功记录: {len(success_df)} 行")
    
    # 计算全局归一化参数
    global_params = compute_global_normalization_params(success_df)
    
    # 应用全局归一化
    normalized_df = apply_global_normalization(df, global_params)
    
    # 生成归一化摘要
    summary_df = generate_normalization_summary(global_params, summary_dir)
    
    # 保存结果
    print(f"\n保存归一化后的数据到: {output_path}")
    normalized_df.to_csv(output_path, index=False)
    
    print(f"\n处理完成!")
    print(f"- 原始数据: {len(df)} 行")
    print(f"- 成功记录: {len(success_df)} 行")
    print(f"- 处理的UQ方法: {len(global_params)} 个")
    print(f"- 输出文件: {output_path}")
    print(f"- 摘要文件: {summary_dir / 'global_normalization_summary.csv'}")
    
    # 显示归一化后的数据统计
    print(f"\n归一化后的数据统计:")

    # 统计各种归一化方法的有效记录数
    normalization_columns = [
        'uq_score_minmax_global',
        'uq_score_zscore_global',
        'uq_score_robust_global',
        'uq_score_maxnorm_global',
        'uq_score_unitvector_global'
    ]

    for col in normalization_columns:
        valid_data = normalized_df.dropna(subset=[col])
        if len(valid_data) > 0:
            print(f"- {col}: {len(valid_data)} 条记录, 范围: [{valid_data[col].min():.4f}, {valid_data[col].max():.4f}]")
        else:
            print(f"- {col}: 无有效数据")

    # 显示应用归一化的记录总数
    applied_count = len(normalized_df[normalized_df['normalization_applied'] == True])
    print(f"- 总计应用归一化的记录: {applied_count} 行")

if __name__ == '__main__':
    main()
