#!/usr/bin/env python3
"""
为topic_labeling任务生成分离E5和Qwen模型的可视化
- X轴：UQ方法的不确定性分数（自动归一化到0-1范围）
- Y轴：到reference的距离（0-1范围）
- 颜色：区分E5和Qwen两个embedding模型

样式配置说明：
- 修改图表样式请编辑下面的 EmbeddingPlotStyle 类
- 可以调整图形大小、字体大小、颜色、透明度等所有参数
- 支持自动数据归一化和统一的坐标轴范围设置
- 使用 --no-title 参数可生成无标题版本的图表
"""
import argparse
from pathlib import Path
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# ==================== 样式配置区域 - 在这里修改图表样式 ====================
class EmbeddingPlotStyle:
    """Embedding图表的统一样式配置"""

    def __init__(self):
        # 图形尺寸设置
        self.figure_size = (8, 8)      # 图形大小 (宽, 高) 英寸
        self.figure_dpi = 300           # 图形分辨率

        # 字体大小设置
        self.font_size_title = 16       # 标题字体大小
        self.font_size_label = 16       # 坐标轴标签字体大小
        self.font_size_tick = 16        # 刻度标签字体大小
        self.font_size_annotation = 16  # 注释字体大小

        # 散点图设置
        self.scatter_size = 30          # 散点大小
        self.scatter_alpha = 0.7        # 散点透明度 (0-1)
        self.scatter_edge_width = 0.5   # 散点边框宽度
        self.scatter_edge_color = 'white'  # 散点边框颜色

        # 线条设置
        self.line_width = 2.5           # 拟合线宽度
        self.line_alpha = 0.8           # 拟合线透明度 (0-1)

        # 颜色设置 (使用十六进制颜色代码)
        self.colors = {
            'e5': '#1f77b4',           # E5模型颜色 (蓝色)
            'qwen': '#ff7f0e',         # Qwen模型颜色 (橙色)
            'fit_line': '#d62728',     # 拟合线颜色 (红色)
        }

        # 坐标轴范围设置
        self.x_min, self.x_max = 0, 1   # X轴范围
        self.y_min, self.y_max = 0, 1   # Y轴范围

        # 网格设置
        self.grid_alpha = 0.3           # 网格透明度 (0-1)

        # 归一化设置
        self.normalize_threshold = 1.0  # 超过此值时进行归一化
        self.show_normalization_info = True  # 是否在标签中显示归一化信息

        # 注释框设置
        self.annotation_colors = {
            'formula': '#f5deb3',      # 公式框颜色 (wheat)
            'r2': '#add8e6',           # R²框颜色 (lightblue)
            'pvalue': '#f08080',       # p值框颜色 (lightcoral)
            'sample': '#90ee90'        # 样本数框颜色 (lightgreen)
        }

    def apply_global_style(self):
        """应用全局matplotlib样式设置"""
        plt.rcParams.update({
            'font.size': 18,
            'axes.titlesize': self.font_size_title,
            'axes.labelsize': self.font_size_label,
            'xtick.labelsize': self.font_size_tick,
            'ytick.labelsize': self.font_size_tick,
            'figure.figsize': self.figure_size,
            'figure.dpi': self.figure_dpi,
            'lines.linewidth': self.line_width,
            # ACM论文专用带线衬字体设置
            'font.family': 'serif',
            'font.serif': ['Times New Roman', 'Times', 'DejaVu Serif', 'Bitstream Vera Serif', 'Computer Modern Roman', 'New Century Schoolbook', 'Century Schoolbook L', 'Utopia', 'ITC Bookman', 'Bookman', 'Nimbus Roman No9 L', 'Palatino', 'Charter', 'serif'],
            'mathtext.fontset': 'cm',  # Computer Modern数学字体
            'axes.unicode_minus': False
        })

    def normalize_data_global(self, data, global_min, global_max, method_name):
        """使用全局Min-Max进行数据归一化处理"""
        if global_max > global_min:
            normalized_data = (data - global_min) / (global_max - global_min)
            if self.show_normalization_info:
                info_suffix = f' (global norm: [{global_min:.3f}, {global_max:.3f}])'
            else:
                info_suffix = ''
            return normalized_data, info_suffix
        else:
            # 如果全局最大值等于最小值，说明所有值都相同
            return np.ones_like(data) * 0.5, ' (constant values)'

# 创建全局样式配置实例
STYLE = EmbeddingPlotStyle()
# ==================== 样式配置区域结束 ====================

def safe_float(v):
    try:
        if pd.isna(v):
            return np.nan
        return float(v)
    except Exception:
        return np.nan

# 全局归一化参数计算函数已移除，现在直接使用预处理的归一化数据

def load_task_dataframe(task: str, use_normalized: bool = True) -> pd.DataFrame:
    """加载任务数据，优先使用合并后的归一化数据"""
    if use_normalized:
        # 对于topic_labeling，优先使用新格式数据
        if task == 'topic_labeling':
            new_format_path = Path(f'data/UQ_result_{task}_new_format.csv')
            if new_format_path.exists():
                print(f"使用新格式数据: {new_format_path}")
                return pd.read_csv(new_format_path)

        # 首先尝试加载合并后的归一化数据（新格式）
        merged_path = Path(f'data/UQ_result_{task}_normalized_with_reference.csv')
        if merged_path.exists():
            print(f"使用合并后的归一化数据: {merged_path}")
            return pd.read_csv(merged_path)

        # 回退到全局归一化数据
        normalized_path = Path('data/combined_uq_results_normalized.csv')
        if normalized_path.exists():
            print(f"使用全局归一化数据: {normalized_path}")
            df = pd.read_csv(normalized_path)
            # 过滤指定任务的数据
            task_df = df[df['task_name'] == task].copy()
            if len(task_df) > 0:
                return task_df
            else:
                print(f"警告: 归一化数据中未找到任务 '{task}' 的数据，回退到原始数据")

    # 回退到原始数据
    csv_path = Path(f'data/UQ_result_{task}.csv')
    if not csv_path.exists():
        raise FileNotFoundError(f"未找到数据文件: {csv_path}")
    print(f"使用原始数据: {csv_path}")
    return pd.read_csv(csv_path)

def extract_embedding_data_separated(df: pd.DataFrame, task: str = '') -> pd.DataFrame:
    """分别提取E5和Qwen的reference距离数据或sentiment accuracy数据

    新数据格式：每个document一行，UQ方法作为列
    """

    # 根据任务类型选择不同的处理方式
    if task == 'sentiment_analysis':
        # 对于sentiment analysis，使用sentiment_accuracy
        if 'sentiment_accuracy' not in df.columns:
            raise ValueError('sentiment_analysis任务但未找到sentiment_accuracy列')

        # 选择有sentiment_accuracy的行
        emb_df = df[df['sentiment_accuracy'].notna()].copy()
        if emb_df.empty:
            raise ValueError('CSV 中未找到有效的 sentiment_accuracy 数据。')

        # 创建两行数据：一行用于E5，一行用于Qwen
        # 检查是否有embedding方法的数据
        has_e5 = 'EmbeddingE5UQ_avg_distance_to_reference' in df.columns
        has_qwen = 'EmbeddingQwenUQ_avg_distance_to_reference' in df.columns

        result_rows = []

        for _, row in emb_df.iterrows():
            # 对于sentiment_analysis，只需要一行数据，因为accuracy与embedding模型无关
            # 只有当至少有一个embedding方法有数据时才添加
            if (has_e5 and pd.notna(row.get('EmbeddingE5UQ_avg_distance_to_reference'))) or \
               (has_qwen and pd.notna(row.get('EmbeddingQwenUQ_avg_distance_to_reference'))):
                result_rows.append({
                    'document_id': row['document_id'],
                    'llm_model': row['llm_model'],
                    'embedding_model': 'Accuracy',  # 标记为accuracy数据
                    'reference_distance': safe_float(row['sentiment_accuracy'])
                })

        if not result_rows:
            raise ValueError('未找到有效的embedding数据')

        result_df = pd.DataFrame(result_rows)
        print("使用sentiment_accuracy作为评估指标")

    else:
        # 对于其他任务，使用avg_distance_to_reference
        has_e5 = 'EmbeddingE5UQ_avg_distance_to_reference' in df.columns
        has_qwen = 'EmbeddingQwenUQ_avg_distance_to_reference' in df.columns

        if not has_e5 and not has_qwen:
            raise ValueError('CSV 中未找到 Embedding UQ方法的distance数据。')

        result_rows = []

        for _, row in df.iterrows():
            # E5数据行
            if has_e5 and pd.notna(row.get('EmbeddingE5UQ_avg_distance_to_reference')):
                result_rows.append({
                    'document_id': row['document_id'],
                    'llm_model': row['llm_model'],
                    'embedding_model': 'E5',
                    'reference_distance': safe_float(row['EmbeddingE5UQ_avg_distance_to_reference'])
                })

            # Qwen数据行
            if has_qwen and pd.notna(row.get('EmbeddingQwenUQ_avg_distance_to_reference')):
                result_rows.append({
                    'document_id': row['document_id'],
                    'llm_model': row['llm_model'],
                    'embedding_model': 'Qwen',
                    'reference_distance': safe_float(row['EmbeddingQwenUQ_avg_distance_to_reference'])
                })

        if not result_rows:
            raise ValueError('未找到有效的embedding distance数据')

        result_df = pd.DataFrame(result_rows)
        print("使用avg_distance_to_reference作为评估指标")

    # 只保留有效的reference距离/准确率
    result_df = result_df.dropna(subset=['reference_distance'])

    return result_df

def extract_uq_scores(df: pd.DataFrame, methods: list, use_normalized: bool = True) -> pd.DataFrame:
    """提取指定UQ方法的分数，优先使用归一化后的分数

    新数据格式：每个document一行，UQ方法作为列
    """
    if df.empty:
        return pd.DataFrame({'document_id': []})

    # 创建结果DataFrame，包含document_id和每个方法的分数
    result_df = df[['document_id']].copy()

    for method in methods:
        # 构建可能的列名，优先使用归一化数据
        possible_columns = []

        # 如果有归一化数据，优先使用
        if use_normalized:
            possible_columns.extend([
                f'{method}_uq_score_minmax_global',  # 归一化分数（优先）
                f'{method}_uq_score_zscore_global',  # Z-score归一化
                f'{method}_uq_score_robust_global',  # 鲁棒归一化
            ])

        # 回退到原始数据
        possible_columns.extend([
            f'{method}_uncertainty_score',  # 不确定性分数
            f'{method}_uq_value',  # 原始UQ值
        ])

        # 寻找可用的列
        score_column = None
        for col in possible_columns:
            if col in df.columns:
                score_column = col
                break

        if score_column:
            result_df[method] = df[score_column].apply(safe_float)
            print(f'方法 {method}: 使用列 {score_column}, 有效数据 {result_df[method].notna().sum()}/{len(result_df)}')
        else:
            print(f"警告: 未找到方法 {method} 的数据列")
            result_df[method] = np.nan

    return result_df

def linear_fit(x: np.ndarray, y: np.ndarray) -> dict:
    """线性拟合，包含p-value计算"""
    mask = (~np.isnan(x)) & (~np.isnan(y))
    x_clean = x[mask]
    y_clean = y[mask]

    if len(x_clean) < 3:  # 至少需要3个点来计算p-value
        return {"coef": None, "intercept": None, "r2": None, "p_value": None, "n": len(x_clean)}

    # 使用numpy进行线性拟合
    coef = np.polyfit(x_clean, y_clean, 1)
    y_pred = np.polyval(coef, x_clean)

    # 计算R²
    ss_tot = np.sum((y_clean - np.mean(y_clean))**2)
    ss_res = np.sum((y_clean - y_pred)**2)
    r2 = 1 - (ss_res / ss_tot) if ss_tot > 0 else None

    # 计算p-value
    n = len(x_clean)
    slope = coef[0]

    # 计算标准误差
    mse = ss_res / (n - 2)  # 均方误差
    x_mean = np.mean(x_clean)
    sxx = np.sum((x_clean - x_mean)**2)
    se_slope = np.sqrt(mse / sxx)  # 斜率的标准误差

    # t统计量
    t_stat = slope / se_slope if se_slope > 0 else 0

    # 计算p-value (双尾检验)
    from scipy import stats
    p_value = 2 * (1 - stats.t.cdf(abs(t_stat), n - 2))

    return {
        "coef": float(coef[0]),
        "intercept": float(coef[1]),
        "r2": float(r2) if r2 is not None else None,
        "p_value": float(p_value),
        "n": int(len(x_clean))
    }

def create_accuracy_bins(accuracy_values) -> pd.Categorical:
    """将accuracy值分为20%的区间"""
    bins = [0.0, 0.2, 0.4, 0.6, 0.8, 1.0]
    labels = ['0-20%', '20-40%', '40-60%', '60-80%', '80-100%']

    # 使用pd.cut来分组
    binned = pd.cut(accuracy_values, bins=bins, labels=labels, include_lowest=True)
    return binned

def create_box_plot(merged_data: pd.DataFrame, method: str, llm_model: str, task_fig_dir: Path, args) -> None:
    """创建accuracy区间的box plot"""

    # 创建accuracy区间
    merged_data['accuracy_bin'] = create_accuracy_bins(merged_data['reference_distance'].values)

    # 移除NaN值
    plot_data = merged_data.dropna(subset=['accuracy_bin', method])

    if len(plot_data) == 0:
        print(f"没有有效数据用于绘制 {method} 的box plot")
        return

    plt.figure(figsize=STYLE.figure_size)

    # 准备box plot数据
    box_data = []
    box_labels = []

    for bin_name in ['0-20%', '20-40%', '40-60%', '60-80%', '80-100%']:
        bin_data = plot_data[plot_data['accuracy_bin'] == bin_name][method].values
        if len(bin_data) > 0:
            box_data.append(bin_data)
            box_labels.append(bin_name)

    if len(box_data) == 0:
        print(f"没有有效的区间数据用于绘制 {method} 的box plot")
        return

    # 创建box plot
    box_plot = plt.boxplot(box_data, tick_labels=box_labels, patch_artist=True)

    # 设置box plot样式
    for patch in box_plot['boxes']:
        patch.set_facecolor(STYLE.colors['e5'])
        patch.set_alpha(0.7)

    plt.xlabel('Accuracy Range', fontsize=STYLE.font_size_label)
    plt.ylabel(f'{method} Score', fontsize=STYLE.font_size_label)

    # 根据参数决定是否显示标题
    if not args.no_title:
        plt.title(f'{args.task}: {llm_model} - {method} Distribution by Accuracy Range', fontsize=STYLE.font_size_title)

    plt.grid(True, alpha=STYLE.grid_alpha)
    plt.tight_layout()
    plt.subplots_adjust(bottom=0.15)  # 为底部文本留出空间

    # 保存图像
    safe_llm_name = llm_model.replace(':', '_').replace('/', '_')
    title_suffix = '_notitle' if args.no_title else ''
    png_file = task_fig_dir / f'{safe_llm_name}_{method}_AccuracyBoxPlot{title_suffix}.png'
    pdf_file = task_fig_dir / f'{safe_llm_name}_{method}_AccuracyBoxPlot{title_suffix}.pdf'
    plt.savefig(png_file, dpi=STYLE.figure_dpi, bbox_inches='tight')
    plt.savefig(pdf_file, bbox_inches='tight')
    plt.close()
    print(f'保存 {llm_model} - {method} Box Plot图像到 {png_file} 和 {pdf_file}')

def create_scatter_plot(merged_data: pd.DataFrame, method: str, llm_model: str, task_fig_dir: Path, args) -> dict:
    """创建散点图并返回拟合结果"""

    plt.figure(figsize=STYLE.figure_size)

    # 交换X轴和Y轴：accuracy/distance作为X轴，UQ分数作为Y轴
    x_data = np.array(merged_data['reference_distance'].values)  # accuracy/distance作为X轴
    y_data = np.array(merged_data[method].values)  # UQ分数作为Y轴

    # 创建散点图
    plt.scatter(x_data, y_data,
               s=STYLE.scatter_size,
               alpha=STYLE.scatter_alpha,
               color=STYLE.colors['e5'],
               edgecolors=STYLE.scatter_edge_color,
               linewidth=STYLE.scatter_edge_width)

    # 拟合
    fit_result = linear_fit(x_data, y_data)

    if fit_result['coef'] is not None:
        # 限制拟合线在坐标轴范围内 (0-1)
        x_range = np.linspace(max(0, float(np.min(x_data))), min(1, float(np.max(x_data))), 100)
        y_fit = fit_result['coef'] * x_range + fit_result['intercept']
        plt.plot(x_range, y_fit,
                color=STYLE.colors['fit_line'],
                linestyle='-',
                linewidth=STYLE.line_width,
                alpha=STYLE.line_alpha)

        # 添加拟合信息
        coef, intercept, r2, p_value, n = fit_result['coef'], fit_result['intercept'], fit_result['r2'], fit_result['p_value'], fit_result['n']
        if intercept >= 0:
            formula = f'y = {coef:.4f}x + {intercept:.4f}'
        else:
            formula = f'y = {coef:.4f}x - {abs(intercept):.4f}'
        r2_str = f"R² = {r2:.4f}" if r2 is not None else "R² = -"
        p_str = f"p = {p_value:.4f}" if p_value is not None else "p = -"

        # 将所有信息合并为一行，放在图表外部x轴标题下方
        info_text = f'{formula}  |  {r2_str}  |  {p_str}  |  n = {n}'
        plt.figtext(0.5, 0.02, info_text, fontsize=15,
                   horizontalalignment='center', verticalalignment='bottom')

    # 根据任务类型设置轴标签（X轴和Y轴已交换）
    if args.task == 'sentiment_analysis':
        plt.xlabel('Accuracy', fontsize=STYLE.font_size_label)
        plt.ylabel(f'{method} Score', fontsize=STYLE.font_size_label)
        plt.xlim(0, 1)  # 严格限制x轴范围为0-1
        x_metric = 'Accuracy'
    else:
        plt.xlabel('Distance to Reference Text', fontsize=STYLE.font_size_label)
        plt.ylabel(f'{method} Score', fontsize=STYLE.font_size_label)
        plt.xlim(0, 1)  # 严格限制x轴范围为0-1
        x_metric = 'Distance'

    # 根据参数决定是否显示标题
    if not args.no_title:
        plt.title(f'{args.task}: {llm_model} - {x_metric} vs {method}', fontsize=STYLE.font_size_title)

    plt.grid(True, alpha=STYLE.grid_alpha)
    plt.tight_layout()
    plt.subplots_adjust(bottom=0.15)  # 为底部文本留出空间

    # 保存图像
    safe_llm_name = llm_model.replace(':', '_').replace('/', '_')
    title_suffix = '_notitle' if args.no_title else ''
    png_file = task_fig_dir / f'{safe_llm_name}_{method}_AccuracyScatter{title_suffix}.png'
    pdf_file = task_fig_dir / f'{safe_llm_name}_{method}_AccuracyScatter{title_suffix}.pdf'
    plt.savefig(png_file, dpi=STYLE.figure_dpi, bbox_inches='tight')
    plt.savefig(pdf_file, bbox_inches='tight')
    plt.close()
    print(f'保存 {llm_model} - {method} Scatter Plot图像到 {png_file} 和 {pdf_file}')

    return fit_result

def main():
    # 应用样式配置
    STYLE.apply_global_style()

    parser = argparse.ArgumentParser()
    parser.add_argument('--task', default='sentiment_analysis', help='任务名称 (sentiment_analysis, topic_labeling)')
    parser.add_argument('--uq-methods', default='', help='逗号分隔的UQ方法名称，留空则使用所有非embedding方法')
    parser.add_argument('--no-title', action='store_true', help='不显示图表标题')
    args = parser.parse_args()
    
    # 加载数据（优先使用归一化数据）
    df = load_task_dataframe(args.task, use_normalized=True)

    # 检查是否有归一化列
    has_normalized = any('minmax_global' in col for col in df.columns)
    print(f"数据包含归一化列: {has_normalized}")

    if has_normalized:
        minmax_columns = [col for col in df.columns if 'minmax_global' in col]
        print(f"找到 {len(minmax_columns)} 个归一化列")

    # 提取embedding数据（分离E5和Qwen）
    print(f"原始数据总行数: {len(df)}")

    # 检查embedding方法列的存在
    embedding_columns = [col for col in df.columns if 'Embedding' in col and '_uq_value' in col]
    print(f"找到embedding方法列: {embedding_columns}")

    emb_data = extract_embedding_data_separated(df, args.task)
    print(f"提取到 {len(emb_data)} 条embedding数据")
    print(f"E5数据: {len(emb_data[emb_data['embedding_model'] == 'E5'])} 条")
    print(f"Qwen数据: {len(emb_data[emb_data['embedding_model'] == 'Qwen'])} 条")

    # 获取UQ方法列表（排除embedding方法）
    if args.uq_methods.strip():
        uq_methods = [m.strip() for m in args.uq_methods.split(',') if m.strip()]
    else:
        # 从列名中提取UQ方法名（排除embedding方法）
        uq_value_columns = [col for col in df.columns if col.endswith('_uq_value')]
        uq_methods = [col.replace('_uq_value', '') for col in uq_value_columns]

    print(f"将分析以下UQ方法: {uq_methods}")

    # 提取UQ分数（使用归一化数据）
    print(f"准备提取UQ分数，方法列表: {uq_methods}")
    uq_scores = extract_uq_scores(df, uq_methods, use_normalized=has_normalized)
    print(f"UQ分数DataFrame形状: {uq_scores.shape}")
    print(f"UQ分数列名: {uq_scores.columns.tolist()}")
    
    # 创建输出目录
    fig_dir = Path('figures')
    fig_dir.mkdir(parents=True, exist_ok=True)

    # 创建专门的子目录
    task_fig_dir = fig_dir / f'{args.task}_embedding_reference'
    task_fig_dir.mkdir(parents=True, exist_ok=True)
    
    # 获取LLM模型列表，过滤掉unknown模型
    all_llm_models = df['llm_model'].unique()
    llm_models = [model for model in all_llm_models if model != 'unknown']
    print(f"发现LLM模型: {all_llm_models}")
    print(f"将处理的LLM模型: {llm_models}")

    # 为每个LLM模型和UQ方法组合生成图表
    fit_results = {}

    for llm_model in llm_models:
        fit_results[llm_model] = {}

        # 过滤当前LLM模型的数据
        llm_emb_data = emb_data[emb_data['llm_model'] == llm_model]

        for method in uq_methods:
            print(f"\n处理方法: {method}")
            if method not in uq_scores.columns:
                print(f"跳过方法 {method}: 数据中不存在")
                continue

            # 合并数据
            print(f"合并数据: llm_emb_data({len(llm_emb_data)}) + uq_scores({len(uq_scores)})")
            merged = llm_emb_data.merge(uq_scores[['document_id', method]], on='document_id', how='inner')
            print(f"合并后数据: {len(merged)} 行")
            merged = merged.dropna(subset=[method, 'reference_distance'])
            print(f"去除空值后数据: {len(merged)} 行")

            if len(merged) < 2:
                print(f"跳过 {llm_model} - {method}: 有效数据点不足 ({len(merged)})")
                continue

            fit_results[llm_model][method] = {}

            if args.task == 'sentiment_analysis':
                # 对于sentiment analysis，只使用标记为'Accuracy'的数据
                accuracy_data = merged[merged['embedding_model'] == 'Accuracy']
                print(f"为sentiment_analysis任务生成图表，数据量: {len(accuracy_data)}")

                if len(accuracy_data) > 1:
                    # 创建scatter plot并获取拟合结果
                    fit_acc = create_scatter_plot(accuracy_data, method, llm_model, task_fig_dir, args)
                    fit_results[llm_model][method]['Accuracy'] = fit_acc
            else:
                # 对于其他任务，分离E5和Qwen数据
                e5_data = merged[merged['embedding_model'] == 'E5']
                qwen_data = merged[merged['embedding_model'] == 'Qwen']

                # 为E5创建独立图表
                if len(e5_data) > 1:
                    plt.figure(figsize=STYLE.figure_size)

                # 交换X轴和Y轴：distance作为X轴，UQ分数作为Y轴
                x_e5 = e5_data['reference_distance'].values  # distance作为X轴
                y_e5 = e5_data[method].values  # UQ分数作为Y轴

                # 数据已经是归一化的，直接使用
                y_e5_normalized = y_e5
                if has_normalized:
                    y_label_suffix = ' (globally normalized)'
                else:
                    y_label_suffix = ''

                plt.scatter(x_e5, y_e5_normalized,
                           s=STYLE.scatter_size,
                           alpha=STYLE.scatter_alpha,
                           color=STYLE.colors['e5'],
                           edgecolors=STYLE.scatter_edge_color,
                           linewidth=STYLE.scatter_edge_width)

                # E5拟合（使用归一化后的数据）
                fit_e5 = linear_fit(x_e5, y_e5_normalized)
                fit_results[llm_model][method]['E5'] = fit_e5

                if fit_e5['coef'] is not None:
                    # 限制拟合线在坐标轴范围内 (0-1)
                    x_range = np.linspace(max(0, np.min(x_e5)), min(1, np.max(x_e5)), 100)
                    y_fit = fit_e5['coef'] * x_range + fit_e5['intercept']
                    plt.plot(x_range, y_fit,
                            color=STYLE.colors['fit_line'],
                            linestyle='-',
                            linewidth=STYLE.line_width,
                            alpha=STYLE.line_alpha)

                    # 添加拟合信息
                    coef, intercept, r2, p_value, n = fit_e5['coef'], fit_e5['intercept'], fit_e5['r2'], fit_e5['p_value'], fit_e5['n']
                    if intercept >= 0:
                        formula = f'y = {coef:.4f}x + {intercept:.4f}'
                    else:
                        formula = f'y = {coef:.4f}x - {abs(intercept):.4f}'
                    r2_str = f"R² = {r2:.4f}" if r2 is not None else "R² = -"
                    p_str = f"p = {p_value:.4f}" if p_value is not None else "p = -"

                    # 将所有信息合并为一行，放在图表外部x轴标题下方
                    info_text = f'{formula}  |  {r2_str}  |  {p_str}  |  n = {n}'
                    plt.figtext(0.5, 0.02, info_text, fontsize=15,
                               horizontalalignment='center', verticalalignment='bottom')

                # 对于topic_labeling，X轴和Y轴已经交换
                plt.xlabel('Distance to Reference Text (E5)', fontsize=STYLE.font_size_label)
                plt.ylabel(f'{method} Score{y_label_suffix}', fontsize=STYLE.font_size_label)
                plt.xlim(0, 1)  # 严格限制x轴范围为0-1

                # 根据参数决定是否显示标题
                if not args.no_title:
                    plt.title(f'{args.task}: {llm_model} - Distance vs {method} (E5)', fontsize=STYLE.font_size_title)

                # 设置坐标轴范围（使用样式配置）
                plt.xlim(STYLE.x_min, STYLE.x_max)
                plt.ylim(STYLE.y_min, STYLE.y_max)

                plt.grid(True, alpha=STYLE.grid_alpha)
                plt.tight_layout()
                plt.subplots_adjust(bottom=0.15)  # 为底部文本留出空间

                # 保存E5图像 (PNG和PDF)
                safe_llm_name = llm_model.replace(':', '_').replace('/', '_')
                title_suffix = '_notitle' if args.no_title else ''
                png_file_e5 = task_fig_dir / f'{safe_llm_name}_{method}_E5{title_suffix}.png'
                pdf_file_e5 = task_fig_dir / f'{safe_llm_name}_{method}_E5{title_suffix}.pdf'
                plt.savefig(png_file_e5, dpi=STYLE.figure_dpi, bbox_inches='tight')
                plt.savefig(pdf_file_e5, bbox_inches='tight')
                plt.close()
                print(f'保存 {llm_model} - {method} E5图像到 {png_file_e5} 和 {pdf_file_e5}')

                # 为Qwen创建独立图表
                if len(qwen_data) > 1:
                    plt.figure(figsize=STYLE.figure_size)

                # 交换X轴和Y轴：distance作为X轴，UQ分数作为Y轴
                x_qwen = qwen_data['reference_distance'].values  # distance作为X轴
                y_qwen = qwen_data[method].values  # UQ分数作为Y轴

                # 数据已经是归一化的，直接使用
                y_qwen_normalized = y_qwen
                if has_normalized:
                    y_label_suffix_qwen = ' (globally normalized)'
                else:
                    y_label_suffix_qwen = ''

                plt.scatter(x_qwen, y_qwen_normalized,
                           s=STYLE.scatter_size,
                           alpha=STYLE.scatter_alpha,
                           color=STYLE.colors['qwen'],
                           edgecolors=STYLE.scatter_edge_color,
                           linewidth=STYLE.scatter_edge_width)

                # Qwen拟合（使用归一化后的数据）
                fit_qwen = linear_fit(x_qwen, y_qwen_normalized)
                fit_results[llm_model][method]['Qwen'] = fit_qwen

                if fit_qwen['coef'] is not None:
                    # 限制拟合线在坐标轴范围内 (0-1)
                    x_range = np.linspace(max(0, np.min(x_qwen)), min(1, np.max(x_qwen)), 100)
                    y_fit = fit_qwen['coef'] * x_range + fit_qwen['intercept']
                    plt.plot(x_range, y_fit,
                            color=STYLE.colors['fit_line'],
                            linestyle='-',
                            linewidth=STYLE.line_width,
                            alpha=STYLE.line_alpha)

                    # 添加拟合信息
                    coef, intercept, r2, p_value, n = fit_qwen['coef'], fit_qwen['intercept'], fit_qwen['r2'], fit_qwen['p_value'], fit_qwen['n']
                    if intercept >= 0:
                        formula = f'y = {coef:.4f}x + {intercept:.4f}'
                    else:
                        formula = f'y = {coef:.4f}x - {abs(intercept):.4f}'
                    r2_str = f"R² = {r2:.4f}" if r2 is not None else "R² = -"
                    p_str = f"p = {p_value:.4f}" if p_value is not None else "p = -"

                    # 将所有信息合并为一行，放在图表外部x轴标题下方
                    info_text = f'{formula}  |  {r2_str}  |  {p_str}  |  n = {n}'
                    plt.figtext(0.5, 0.02, info_text, fontsize=15,
                               horizontalalignment='center', verticalalignment='bottom')

                # 对于topic_labeling，X轴和Y轴已经交换
                plt.xlabel('Distance to Reference Text (Qwen)', fontsize=STYLE.font_size_label)
                plt.ylabel(f'{method} Score{y_label_suffix_qwen}', fontsize=STYLE.font_size_label)
                plt.xlim(0, 1)  # 严格限制x轴范围为0-1

                # 根据参数决定是否显示标题
                if not args.no_title:
                    plt.title(f'{args.task}: {llm_model} - Distance vs {method} (Qwen)', fontsize=STYLE.font_size_title)

                # 设置坐标轴范围（使用样式配置）
                plt.xlim(STYLE.x_min, STYLE.x_max)
                plt.ylim(STYLE.y_min, STYLE.y_max)

                plt.grid(True, alpha=STYLE.grid_alpha)
                plt.tight_layout()
                plt.subplots_adjust(bottom=0.15)  # 为底部文本留出空间

                # 保存Qwen图像 (PNG和PDF)
                safe_llm_name = llm_model.replace(':', '_').replace('/', '_')
                title_suffix = '_notitle' if args.no_title else ''
                png_file_qwen = task_fig_dir / f'{safe_llm_name}_{method}_Qwen{title_suffix}.png'
                pdf_file_qwen = task_fig_dir / f'{safe_llm_name}_{method}_Qwen{title_suffix}.pdf'
                plt.savefig(png_file_qwen, dpi=STYLE.figure_dpi, bbox_inches='tight')
                plt.savefig(pdf_file_qwen, bbox_inches='tight')
                plt.close()
                print(f'保存 {llm_model} - {method} Qwen图像到 {png_file_qwen} 和 {pdf_file_qwen}')
    
    # 保存拟合结果为CSV格式
    fit_csv = task_fig_dir / 'fitting_results.csv'

    # 将嵌套字典转换为DataFrame
    rows = []
    for llm_model in fit_results:
        for method in fit_results[llm_model]:
            for emb_model in fit_results[llm_model][method]:
                data = fit_results[llm_model][method][emb_model]
                row = {
                    'task': args.task,
                    'llm_model': llm_model,
                    'uq_method': method,
                    'embedding_model': emb_model,
                    'coefficient': data['coef'],
                    'intercept': data['intercept'],
                    'r_squared': data['r2'],
                    'p_value': data['p_value'],
                    'sample_size': data['n']
                }
                rows.append(row)

    # 创建DataFrame并保存为CSV
    import pandas as pd
    df_results = pd.DataFrame(rows)
    df_results.to_csv(fit_csv, index=False)

    print(f'拟合参数保存到 {fit_csv}')
    print(f'共生成 {len(fit_results)} 个分离图表，保存在 {task_fig_dir}')
    print(f'每个UQ方法生成了E5和Qwen两个图表，格式包括PNG和PDF')

if __name__ == '__main__':
    main()
