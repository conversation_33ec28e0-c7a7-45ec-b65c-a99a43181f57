#!/usr/bin/env python3
"""
重新提取sentiment analysis数据，将UQ方法作为列而不是行
每个document一行，包含所有UQ方法的值和sentiment accuracy
"""

import pandas as pd
import numpy as np
from pymongo import MongoClient
from pathlib import Path
from typing import Dict, List, Any, Optional
from tqdm import tqdm
import argparse

def connect_mongo(host='localhost', port=27017, db_name='LLM-UQ'):
    """连接MongoDB"""
    client = MongoClient(host, port)
    return client[db_name]

def get_responses_and_reference_from_mongo(db, document_id: str) -> Dict[str, Any]:
    """从MongoDB获取指定document的responses和reference答案"""
    try:
        from bson import ObjectId

        # 从UQ collection获取document信息
        uq_doc = db['UQ_result_sentiment_analysis'].find_one({'_id': ObjectId(document_id)})
        if not uq_doc or 'group_key' not in uq_doc:
            return {}

        group_key = uq_doc['group_key']

        # 使用group_key在response_collections中查找匹配的responses
        query = {
            'task_name': group_key['task_name'],
            'dataset_source': group_key['dataset_source'],
            'prompt_seed': group_key['prompt_seed'],
            'input_text': group_key['input_text'],
            'model_identifier': uq_doc.get('llm_model', 'qwen3-32b')  # 使用model_identifier字段
        }

        response_docs = list(db['response_collections'].find(query))

        if not response_docs:
            return {}

        # 提取parsed_answer作为responses
        responses = []
        reference_answer = None

        for doc in response_docs:
            parsed_answer = doc.get('parsed_answer')
            if parsed_answer:
                responses.append(str(parsed_answer).strip())

            # 获取reference答案
            if reference_answer is None:
                reference_answer = doc.get('reference_answer', '')

        return {
            'responses': responses,
            'reference_answer': reference_answer,
            'input_text': group_key.get('input_text', ''),
            'model': uq_doc.get('llm_model', 'qwen3-32b')
        }

    except Exception as e:
        print(f"Error getting responses for document {document_id}: {e}")
        return {}

def compute_sentiment_accuracy(responses: List[str], reference_answer: str) -> Optional[float]:
    """计算sentiment analysis的准确率"""
    try:
        if not responses or not reference_answer:
            return None

        reference_lower = reference_answer.lower().strip()
        total = len(responses)
        correct = 0

        for response in responses:
            if response is None:
                continue
            response_lower = response.lower().strip()
            if response_lower == reference_lower:
                correct += 1

        return correct / total if total > 0 else None

    except Exception as e:
        print(f"Error computing sentiment accuracy: {e}")
        return None

def extract_document_data(db, document_id: str) -> Dict[str, Any]:
    """提取单个document的完整数据（基础信息 + 所有UQ方法结果）"""
    try:
        from bson import ObjectId

        # 从UQ collection获取document
        uq_doc = db['UQ_result_sentiment_analysis'].find_one({'_id': ObjectId(document_id)})
        if not uq_doc:
            return {}

        # 提取基础信息
        group_key = uq_doc.get('group_key', {})
        base_info = {
            'document_id': document_id,
            'llm_model': uq_doc.get('llm_model', 'unknown'),
            'task_name': group_key.get('task_name', 'unknown'),
            'dataset_source': group_key.get('dataset_source', 'unknown'),
            'prompt_seed': group_key.get('prompt_seed', None),
            'input_text': group_key.get('input_text', ''),
            'n_responses': uq_doc.get('metadata', {}).get('n_responses', 0),
            'successful_methods': uq_doc.get('metadata', {}).get('successful_methods', 0),
            'failed_methods': uq_doc.get('metadata', {}).get('failed_methods', 0),
            'created_at': uq_doc.get('timestamps', {}).get('created_at', None),
        }

        # 提取所有UQ方法的结果
        uq_results = uq_doc.get('uq_results', {})
        for method_name, method_result in uq_results.items():
            # UQ值
            base_info[f'{method_name}_uq_value'] = method_result.get('uq_value', None)
            base_info[f'{method_name}_status'] = method_result.get('status', 'unknown')

            # 额外的指标
            if 'full_result' in method_result:
                full_result = method_result['full_result']
                base_info[f'{method_name}_uncertainty_score'] = full_result.get('uncertainty_score', None)
                base_info[f'{method_name}_mean_similarity'] = full_result.get('mean_similarity', None)
                base_info[f'{method_name}_num_responses'] = full_result.get('num_responses', None)

                # embedding方法的特殊指标
                if 'avg_distance_to_reference' in full_result:
                    base_info[f'{method_name}_avg_distance_to_reference'] = full_result['avg_distance_to_reference']
                if 'avg_candidate_distance' in full_result:
                    base_info[f'{method_name}_avg_candidate_distance'] = full_result['avg_candidate_distance']

        # 计算sentiment accuracy
        doc_data = get_responses_and_reference_from_mongo(db, document_id)
        if doc_data:
            responses = doc_data.get('responses', [])
            reference_answer = doc_data.get('reference_answer', '')
            accuracy = compute_sentiment_accuracy(responses, reference_answer)
            base_info['sentiment_accuracy'] = accuracy
            base_info['reference_answer'] = reference_answer
            base_info['num_responses_found'] = len(responses)

        return base_info

    except Exception as e:
        print(f"Error extracting data for document {document_id}: {e}")
        return {}

def extract_all_documents_data(output_path: Path, limit: int = 0):
    """提取所有documents的数据，每个document一行，UQ方法作为列"""
    print("Connecting to MongoDB...")
    db = connect_mongo()

    # 获取所有documents
    collection = db['UQ_result_sentiment_analysis']
    total_docs = collection.count_documents({})
    print(f"Found {total_docs} documents in UQ_result_sentiment_analysis")

    # 获取document IDs
    if limit > 0:
        docs = list(collection.find({}, {'_id': 1}).limit(limit))
    else:
        docs = list(collection.find({}, {'_id': 1}))

    document_ids = [str(doc['_id']) for doc in docs]
    print(f"Processing {len(document_ids)} documents")

    # 提取所有数据
    all_data = []

    for i, doc_id in enumerate(tqdm(document_ids, desc="Extracting document data")):
        if i % 10 == 0:  # 每10个文档打印一次进度
            print(f"\n[{i+1}/{len(document_ids)}] Processing document: {doc_id}")

        doc_data = extract_document_data(db, doc_id)
        if doc_data:
            all_data.append(doc_data)
            if i % 10 == 0:
                print(f"  Extracted data with {len(doc_data)} fields")
                if 'sentiment_accuracy' in doc_data:
                    print(f"  Sentiment accuracy: {doc_data['sentiment_accuracy']}")
        else:
            if i % 10 == 0:
                print(f"  No data extracted for document {doc_id}")

    if not all_data:
        print("No data extracted!")
        return

    # 转换为DataFrame
    print(f"\nConverting {len(all_data)} records to DataFrame...")
    df = pd.DataFrame(all_data)

    print(f"DataFrame shape: {df.shape}")
    print(f"Columns: {len(df.columns)}")

    # 显示一些统计信息
    print(f"\nBasic statistics:")
    print(f"Unique models: {df['llm_model'].nunique()}")
    print(f"Model distribution: {df['llm_model'].value_counts().to_dict()}")

    if 'sentiment_accuracy' in df.columns:
        non_null_accuracy = df['sentiment_accuracy'].notna().sum()
        print(f"Documents with sentiment_accuracy: {non_null_accuracy}/{len(df)} ({non_null_accuracy/len(df)*100:.2f}%)")

        if non_null_accuracy > 0:
            accuracy_stats = df['sentiment_accuracy'].describe()
            print(f"Sentiment accuracy statistics:")
            print(accuracy_stats)

    # 显示UQ方法列
    uq_columns = [col for col in df.columns if '_uq_value' in col]
    print(f"\nFound {len(uq_columns)} UQ methods:")
    for col in sorted(uq_columns):
        method_name = col.replace('_uq_value', '')
        non_null_count = df[col].notna().sum()
        print(f"  {method_name}: {non_null_count}/{len(df)} ({non_null_count/len(df)*100:.2f}%)")

    # 保存结果
    print(f"\nSaving results to: {output_path}")
    df.to_csv(output_path, index=False)

    print(f"Successfully saved {len(df)} rows with {len(df.columns)} columns")

def convert_old_format_to_new(input_file: str, output_file: str, task_name: str):
    """将旧格式数据（每个UQ方法一行）转换为新格式（每个document一行，UQ方法作为列）"""
    print(f"转换 {task_name} 数据格式...")
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")

    # 读取旧格式数据
    df = pd.read_csv(input_file)
    print(f"原始数据: {len(df)} 行, {len(df.columns)} 列")

    if 'uq_method' not in df.columns:
        raise ValueError("输入文件不是旧格式（缺少uq_method列）")

    # 检查UQ方法
    uq_methods = df['uq_method'].unique()
    print(f"找到 {len(uq_methods)} 个UQ方法")

    # 按document_id分组
    grouped = df.groupby('document_id')
    print(f"找到 {len(grouped)} 个不同的documents")

    # 转换数据
    new_rows = []

    for doc_id, group in tqdm(grouped, desc="转换documents"):
        # 获取基础信息（从第一行）
        first_row = group.iloc[0]
        base_info = {
            'document_id': doc_id,
            'llm_model': first_row.get('llm_model', 'unknown'),
            'task_name': first_row.get('task_name', task_name),
            'dataset_source': first_row.get('dataset_source', 'unknown'),
            'prompt_seed': first_row.get('prompt_seed', None),
            'input_text': first_row.get('input_text', ''),
            'n_responses': first_row.get('n_responses', 0),
            'created_at': first_row.get('created_at', None),
        }

        # 添加任务特定的参考值
        if task_name == 'topic_labeling':
            # 对于topic_labeling，使用avg_distance_to_reference
            base_info['avg_distance_to_reference'] = first_row.get('avg_distance_to_reference', None)
            base_info['reference_answer'] = first_row.get('reference_answer', '')

        # 添加每个UQ方法的结果
        for _, row in group.iterrows():
            method = row['uq_method']

            # UQ值
            base_info[f'{method}_uq_value'] = row.get('uq_value', None)
            base_info[f'{method}_status'] = row.get('status', 'unknown')

            # 归一化分数（重要！）
            base_info[f'{method}_uq_score_minmax_global'] = row.get('uq_score_minmax_global', None)
            base_info[f'{method}_uq_score_zscore_global'] = row.get('uq_score_zscore_global', None)
            base_info[f'{method}_uq_score_robust_global'] = row.get('uq_score_robust_global', None)

            # 其他指标
            base_info[f'{method}_uncertainty_score'] = row.get('uncertainty_score', None)
            base_info[f'{method}_mean_similarity'] = row.get('mean_similarity', None)

            # embedding方法的特殊指标
            if 'avg_distance_to_reference' in row and pd.notna(row['avg_distance_to_reference']):
                base_info[f'{method}_avg_distance_to_reference'] = row['avg_distance_to_reference']
            if 'avg_candidate_distance' in row and pd.notna(row['avg_candidate_distance']):
                base_info[f'{method}_avg_candidate_distance'] = row['avg_candidate_distance']

        new_rows.append(base_info)

    # 创建新的DataFrame
    new_df = pd.DataFrame(new_rows)
    print(f"转换后数据: {len(new_df)} 行, {len(new_df.columns)} 列")

    # 显示统计信息
    print(f"\n=== 转换结果统计 ===")
    print(f"Documents数量: {len(new_df)}")

    if 'llm_model' in new_df.columns:
        model_counts = new_df['llm_model'].value_counts()
        print(f"模型分布: {model_counts.to_dict()}")

    # 检查UQ方法列
    uq_columns = [col for col in new_df.columns if col.endswith('_uq_value')]
    print(f"UQ方法数量: {len(uq_columns)}")

    for col in sorted(uq_columns):
        method_name = col.replace('_uq_value', '')
        non_null = new_df[col].notna().sum()
        print(f"  {method_name}: {non_null}/{len(new_df)} ({non_null/len(new_df)*100:.1f}%)")

    # 保存结果
    new_df.to_csv(output_file, index=False)
    print(f"\n转换完成，保存到: {output_file}")

    return new_df

def main():
    parser = argparse.ArgumentParser(description="数据格式转换和提取工具")
    parser.add_argument('--task', choices=['sentiment_analysis', 'topic_labeling'],
                       default='sentiment_analysis', help='任务类型')
    parser.add_argument('--convert', action='store_true',
                       help='转换旧格式数据到新格式')
    parser.add_argument('--input', help='输入文件路径（用于转换）')
    parser.add_argument('--output', help='输出CSV文件路径')
    parser.add_argument('--limit', type=int, default=0,
                       help='限制处理的document数量（0表示处理全部）')

    args = parser.parse_args()

    if args.convert:
        # 转换模式
        if not args.input or not args.output:
            raise ValueError("转换模式需要指定 --input 和 --output 参数")

        input_path = Path(args.input)
        output_path = Path(args.output)

        if not input_path.exists():
            raise FileNotFoundError(f"输入文件不存在: {input_path}")

        # 创建输出目录
        output_path.parent.mkdir(parents=True, exist_ok=True)

        # 转换数据格式
        convert_old_format_to_new(str(input_path), str(output_path), args.task)

    else:
        # 原有的sentiment_analysis提取模式
        if args.task != 'sentiment_analysis':
            raise ValueError("非转换模式只支持sentiment_analysis任务")

        output_path = Path(args.output or 'data/UQ_result_sentiment_analysis_normalized_with_reference.csv')

        # 创建输出目录
        output_path.parent.mkdir(parents=True, exist_ok=True)

        # 提取所有数据
        extract_all_documents_data(output_path, args.limit)

if __name__ == "__main__":
    main()
