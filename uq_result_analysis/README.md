# UQ Result Analysis Documentation

## 概述

`uq_result_analysis/` 目录包含用于分析大语言模型不确定性量化(UQ)结果的完整工具链。该系统从MongoDB数据库提取UQ分析结果，进行数据处理、归一化、可视化分析，并生成研究报告。

## 目录结构

```
uq_result_analysis/
├── README.md                              # 本文档
├── data_extractor.py                      # 核心：MongoDB数据提取器
├── run_analysis.py                        # 核心：主分析脚本
├── global_normalize_uq.py                 # 全局数据归一化处理
├── compute_embedding_reference.py         # Embedding距离计算
├── compute_sentiment_accuracy.py          # 情感分析准确率计算
├── merge_normalized_with_reference.py     # 数据合并工具
├── embedding_reference_scatter_separated.py # 可视化：散点图生成
├── data/                                  # 数据文件目录
│   ├── combined_uq_results.csv           # 原始合并数据
│   ├── combined_uq_results_normalized.csv # 全局归一化数据
│   ├── comprehensive_uq_statistics.csv   # 完整统计信息
│   ├── global_normalization_summary.csv  # 归一化参数摘要
│   └── *.csv                             # 各任务单独数据文件
└── figures/                               # 图表输出目录
    ├── individual_distributions/          # 单个分布图（63个文件）
    ├── method_comparisons/               # 方法比较图（5个文件）
    ├── overall_analysis/                 # 整体分析图（1个文件）
    └── *_embedding_reference/            # 散点图分析（可选）
```

## 核心组件

### 1. 数据提取层 (`data_extractor.py`)

**功能**: 从MongoDB提取UQ分析结果并转换为CSV格式
- 连接MongoDB数据库 (`LLM-UQ`)
- 提取指定collection的UQ结果数据
- 支持的任务类型：
  - `counterfactual_qa` - 反事实问答
  - `explorative_coding` - 探索性编码
  - `sentiment_analysis` - 情感分析
  - `topic_labeling` - 主题标注

**主要类**: `UQDataExtractor`
- `extract_all_data()`: 提取所有目标collection数据
- `create_combined_dataset()`: 创建合并数据集
- `generate_summary_stats()`: 生成数据摘要统计

**输出文件**:
- `combined_uq_results.csv` - 合并的所有UQ结果
- `data_summary.json` - 数据摘要统计
- 各任务单独的CSV文件

### 2. 主分析引擎 (`run_analysis.py`)

**功能**: 执行完整的UQ分析流程并生成可视化
- 数据预处理和清洗
- UQ值归一化处理 (Min-Max和Z-score)
- 生成多层次分析图表

**分析流程**:
1. **数据归一化**: 对每个UQ方法进行Min-Max归一化到[0,1]
2. **单独分析**: 为每个model-task-UQ组合生成分布图
3. **对比分析**: 为每个model-task组合生成UQ方法比较图
4. **整体分析**: 生成全局UQ方法比较图

**生成的图表类型**:
- 分布图：直方图 + 核密度估计 + 统计线
- 小提琴图：多UQ方法对比
- 归一化对比图：统一尺度下的方法比较

### 3. 数据处理工具

#### 全局归一化 (`global_normalize_uq.py`)
- 对所有UQ方法进行全局Min-Max归一化
- 计算归一化参数并保存统计信息
- 支持多种归一化策略

#### Embedding距离计算 (`compute_embedding_reference.py`)
- 计算responses与reference text的embedding距离
- 支持E5和Qwen两种embedding模型
- 实现缓存机制优化计算效率
- 特殊处理sentiment analysis的准确率计算

#### 情感分析处理 (`compute_sentiment_accuracy.py`)
- 专门处理sentiment analysis任务
- 将UQ方法作为列而非行重新组织数据
- 计算sentiment classification准确率

#### 数据合并 (`merge_normalized_with_reference.py`)
- 合并归一化数据和reference距离数据
- 统一数据格式便于后续分析

### 4. 可视化工具 (`embedding_reference_scatter_separated.py`)

**功能**: 生成embedding reference散点图分析
- X轴：UQ方法的不确定性分数（归一化）
- Y轴：到reference的距离
- 颜色区分：E5和Qwen embedding模型
- 支持线性拟合和统计分析

**样式配置**: `EmbeddingPlotStyle`类
- 图形尺寸、字体大小、颜色方案
- 散点图和线条样式
- 支持无标题版本输出

## 数据流程

```mermaid
graph TD
    A[MongoDB数据库] --> B[data_extractor.py]
    B --> C[combined_uq_results.csv]
    C --> D[global_normalize_uq.py]
    D --> E[归一化数据]
    E --> F[compute_embedding_reference.py]
    F --> G[带reference距离的数据]
    G --> H[run_analysis.py]
    H --> I[分析图表和统计]
    
    C --> J[compute_sentiment_accuracy.py]
    J --> K[情感分析专用数据]
    K --> L[merge_normalized_with_reference.py]
    L --> M[合并最终数据]
    M --> N[embedding_reference_scatter_separated.py]
    N --> O[散点图分析]
```

## 使用方法

### 完整分析流程（推荐）

1. **数据提取**
```bash
cd uq_result_analysis
python data_extractor.py --mongo-host localhost --db-name LLM-UQ
```

2. **全局归一化处理**
```bash
python global_normalize_uq.py --input data/combined_uq_results.csv --output data/combined_uq_results_normalized.csv --summary-dir data
```

3. **运行完整分析（使用全局归一化数据）**
```bash
python run_analysis.py
```

### 可选的高级分析

4. **计算embedding距离（可选）**
```bash
python compute_embedding_reference.py --task sentiment_analysis
python compute_embedding_reference.py --task topic_labeling
```

5. **生成散点图分析（可选）**
```bash
python embedding_reference_scatter_separated.py --task topic_labeling --no-title
```

## 输出文件说明

### 数据文件
- `combined_uq_results.csv` - 原始合并数据
- `combined_uq_results_normalized.csv` - 全局归一化数据
- `comprehensive_uq_statistics.csv` - 完整统计信息
- `normalization_stats.json` - 归一化参数

### 图表文件
- `{model}_{task}_{method}_distribution.pdf` - 单一组合分布图
- `{model}_{task}_normalized_violin.pdf` - 方法对比小提琴图
- `overall_normalized_violin.pdf` - 整体方法比较图

### 分析结果
- `figures/combined_embedding_reference_fitting_results.csv` - 拟合结果
- `figures/{task}_embedding_reference/` - 按任务分类的散点图

## 支持的UQ方法

系统支持多种不确定性量化方法：
- **Embedding方法**: EmbeddingE5UQ, EmbeddingQwenUQ
- **熵方法**: SemanticEntropyNLIUQ, KernelLanguageEntropyUQ
- **图方法**: EigValLaplacianJaccardUQ, EccentricityJaccardUQ
- **集合方法**: NumSetsUQ, LUQUQ
- **其他**: LofreeCPUQ, EccentricityNLIEntailUQ

## 配置和自定义

### 样式配置
编辑 `embedding_reference_scatter_separated.py` 中的 `EmbeddingPlotStyle` 类：
- 图形尺寸和分辨率
- 字体大小和样式
- 颜色方案
- 散点和线条样式

### 数据库配置
在各脚本中修改数据库连接参数：
- `MONGO_HOST` - MongoDB主机地址
- `MONGO_PORT` - MongoDB端口
- `DB_NAME` - 数据库名称

## 依赖要求

- Python 3.8+
- pandas, numpy - 数据处理
- matplotlib, seaborn - 可视化
- pymongo - MongoDB连接
- scipy - 统计计算
- tqdm - 进度显示

## 注意事项

1. **数据完整性**: 确保MongoDB中有完整的UQ结果数据
2. **内存使用**: 大数据集可能需要较多内存
3. **计算时间**: embedding距离计算较耗时，已实现缓存优化
4. **文件路径**: 脚本假设在 `uq_result_analysis/` 目录下运行

## 故障排除

### 常见问题
1. **MongoDB连接失败**: 检查数据库服务和连接参数
2. **数据文件缺失**: 确保先运行数据提取步骤
3. **图表生成失败**: 检查matplotlib后端和字体配置
4. **内存不足**: 考虑分批处理或增加系统内存

### 调试建议
- 使用 `--limit` 参数限制处理数据量进行测试
- 检查日志输出了解处理进度
- 验证中间文件的数据格式和完整性

## 项目完成状态

### ✅ 已完成功能
1. **数据提取与处理**
   - MongoDB数据提取：17,291条记录，19种UQ方法，4个任务，3个模型
   - 全局归一化处理：Min-Max [0,1] 和 Z-score标准化
   - 数据质量验证和统计摘要生成

2. **可视化分析**
   - 63个单个分布图（直方图+核密度估计）
   - 5个方法比较图（归一化violin图）
   - 1个整体分析图（全局方法对比）
   - 所有图表均为无标题版本，适合论文发表

3. **精细化文件组织**
   - 数据文件统一保存在 `data/` 目录
   - 图表按类型分类保存在 `figures/` 子目录
   - 完整的统计信息和归一化参数文档

4. **全局归一化优势**
   - 跨UQ方法的一致性比较
   - 统一的[0,1]尺度便于解释
   - 保留原始统计参数用于回溯分析

### 📊 关键数据指标
- **总样本数**: 17,281条有效记录
- **UQ方法**: 19种不同的不确定性量化方法
- **模型覆盖**: qwen3-32b, gpt-oss:20b, phi4:latest
- **任务类型**: sentiment_analysis, topic_labeling, explorative_coding, counterfactual_qa
- **图表输出**: 69个PDF文件，分类保存
- **数据文件**: 8个CSV文件，包含完整统计信息

### 🎯 使用建议
1. 查看 `figures/overall_analysis/` 了解全局UQ方法表现
2. 查看 `figures/method_comparisons/` 了解特定任务下的方法对比
3. 查看 `figures/individual_distributions/` 了解单个方法的分布特征
4. 使用 `data/comprehensive_uq_statistics.csv` 进行定量分析
5. 参考 `data/global_normalization_summary.csv` 了解归一化参数
