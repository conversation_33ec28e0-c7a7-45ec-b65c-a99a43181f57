#!/usr/bin/env python3
"""
合并归一化数据和reference数据
将全局归一化的UQ分数与embedding reference距离数据结合
"""

import pandas as pd
import numpy as np
from pathlib import Path
import argparse

def merge_data_for_task(task: str) -> pd.DataFrame:
    """合并指定任务的归一化数据和reference数据"""
    
    print(f"处理任务: {task}")
    
    # 读取归一化数据
    norm_path = Path('uq_result_analysis/data/combined_uq_results_normalized.csv')
    if not norm_path.exists():
        raise FileNotFoundError(f"归一化数据文件不存在: {norm_path}")
    
    norm_df = pd.read_csv(norm_path)
    norm_task_df = norm_df[norm_df['task_name'] == task].copy()
    print(f"归一化数据中{task}任务行数: {len(norm_task_df)}")
    
    # 读取reference数据
    ref_path = Path(f'uq_result_analysis/data/UQ_result_{task}_with_reference.csv')
    if not ref_path.exists():
        print(f"警告: reference数据文件不存在: {ref_path}")
        print("将使用归一化数据，但缺少reference距离信息")
        return norm_task_df
    
    ref_df = pd.read_csv(ref_path)
    print(f"Reference数据行数: {len(ref_df)}")
    
    # 检查数据匹配度
    norm_docs = set(norm_task_df['document_id'].unique())
    ref_docs = set(ref_df['document_id'].unique())
    common_docs = norm_docs & ref_docs
    print(f"共同document数量: {len(common_docs)} / {len(norm_docs)}")
    
    if len(common_docs) == 0:
        print("警告: 没有找到匹配的documents，返回归一化数据")
        return norm_task_df
    
    # 创建合并键：document_id + llm_model + uq_method
    norm_task_df['merge_key'] = (norm_task_df['document_id'].astype(str) + '_' + 
                                norm_task_df['llm_model'].astype(str) + '_' + 
                                norm_task_df['uq_method'].astype(str))
    
    ref_df['merge_key'] = (ref_df['document_id'].astype(str) + '_' + 
                          ref_df['llm_model'].astype(str) + '_' + 
                          ref_df['uq_method'].astype(str))
    
    print(f"归一化数据唯一merge_key数量: {norm_task_df['merge_key'].nunique()}")
    print(f"Reference数据唯一merge_key数量: {ref_df['merge_key'].nunique()}")
    
    # 选择要从reference数据中合并的列
    ref_columns_to_merge = [
        'merge_key',
        'avg_distance_to_reference',
        'avg_candidate_distance'
    ]
    
    # 只保留存在的列
    available_ref_columns = [col for col in ref_columns_to_merge if col in ref_df.columns]
    ref_subset = ref_df[available_ref_columns].copy()
    
    print(f"从reference数据合并的列: {available_ref_columns}")
    
    # 执行左连接，保留所有归一化数据
    merged_df = norm_task_df.merge(ref_subset, on='merge_key', how='left', suffixes=('', '_ref'))
    
    # 更新avg_distance_to_reference列
    if 'avg_distance_to_reference_ref' in merged_df.columns:
        # 如果归一化数据中的avg_distance_to_reference是NaN，用reference数据中的值填充
        mask_nan = merged_df['avg_distance_to_reference'].isna()
        mask_has_ref = merged_df['avg_distance_to_reference_ref'].notna()
        update_mask = mask_nan & mask_has_ref
        
        merged_df.loc[update_mask, 'avg_distance_to_reference'] = merged_df.loc[update_mask, 'avg_distance_to_reference_ref']
        
        # 删除临时列
        merged_df.drop('avg_distance_to_reference_ref', axis=1, inplace=True)
        
        print(f"更新了 {update_mask.sum()} 行的avg_distance_to_reference数据")
    
    # 同样处理avg_candidate_distance
    if 'avg_candidate_distance_ref' in merged_df.columns:
        mask_nan = merged_df['avg_candidate_distance'].isna()
        mask_has_ref = merged_df['avg_candidate_distance_ref'].notna()
        update_mask = mask_nan & mask_has_ref
        
        merged_df.loc[update_mask, 'avg_candidate_distance'] = merged_df.loc[update_mask, 'avg_candidate_distance_ref']
        merged_df.drop('avg_candidate_distance_ref', axis=1, inplace=True)
        
        print(f"更新了 {update_mask.sum()} 行的avg_candidate_distance数据")
    
    # 删除临时merge_key列
    merged_df.drop('merge_key', axis=1, inplace=True)
    
    # 验证合并结果
    print(f"合并后数据行数: {len(merged_df)}")
    
    # 检查embedding数据的reference距离
    emb_mask = merged_df['uq_method'].str.contains('Embedding', case=False, na=False)
    emb_data = merged_df[emb_mask]
    if len(emb_data) > 0:
        valid_ref_dist = emb_data['avg_distance_to_reference'].notna().sum()
        valid_cand_dist = emb_data['avg_candidate_distance'].notna().sum()
        print(f"Embedding数据: {len(emb_data)} 行")
        print(f"  有效avg_distance_to_reference: {valid_ref_dist}")
        print(f"  有效avg_candidate_distance: {valid_cand_dist}")
    
    return merged_df

def main():
    parser = argparse.ArgumentParser(description='合并归一化数据和reference数据')
    parser.add_argument('--task', default='topic_labeling', 
                       choices=['topic_labeling', 'sentiment_analysis'],
                       help='任务名称')
    parser.add_argument('--output-suffix', default='_normalized_with_reference',
                       help='输出文件后缀')
    
    args = parser.parse_args()
    
    try:
        # 合并数据
        merged_df = merge_data_for_task(args.task)
        
        # 保存合并后的数据
        output_path = Path(f'uq_result_analysis/data/UQ_result_{args.task}{args.output_suffix}.csv')
        merged_df.to_csv(output_path, index=False)
        print(f"\n合并后的数据已保存到: {output_path}")
        
        # 显示最终统计
        print(f"\n=== 最终数据统计 ===")
        print(f"总行数: {len(merged_df)}")
        print(f"UQ方法数量: {len(merged_df['uq_method'].unique())}")
        print(f"Document数量: {len(merged_df['document_id'].unique())}")
        
        # 检查归一化列
        norm_columns = [col for col in merged_df.columns if 'global' in col]
        print(f"归一化列数量: {len(norm_columns)}")
        
        # 检查embedding数据完整性
        emb_data = merged_df[merged_df['uq_method'].str.contains('Embedding', case=False, na=False)]
        if len(emb_data) > 0:
            print(f"\nEmbedding数据完整性:")
            print(f"  总行数: {len(emb_data)}")
            print(f"  有归一化分数: {emb_data['uq_score_minmax_global'].notna().sum()}")
            print(f"  有reference距离: {emb_data['avg_distance_to_reference'].notna().sum()}")
            print(f"  有candidate距离: {emb_data['avg_candidate_distance'].notna().sum()}")
        
        print(f"\n✅ 数据合并完成！现在可以使用 {output_path} 进行分析")
        
    except Exception as e:
        print(f"错误: {e}")
        return 1
    
    return 0

if __name__ == '__main__':
    import sys
    sys.exit(main())
