#!/usr/bin/env python3
"""
UQ结果分析脚本
运行完整的UQ分析并生成可视化图表
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import json
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体和样式（ACM论文风格：衬线字体）
plt.rcParams['axes.unicode_minus'] = False

# 设置图表样式
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

# 设置图表大小
plt.rcParams['figure.figsize'] = (6, 6)
plt.rcParams['figure.dpi'] = 300

def setup_paper_style():
    """设置论文发表风格 - 较大字体版本"""
    plt.rcParams.update({
        'font.size': 18,           # 较大基础字体
        'axes.titlesize': 20,      # 较大标题字体
        'axes.labelsize': 18,      # 较大坐标轴标签字体
        'xtick.labelsize': 18,     # 较大x轴刻度字体
        'ytick.labelsize': 18,     # 较大y轴刻度字体
        'legend.fontsize': 18,     # 较大图例字体
        'figure.titlesize': 18,    # 较大图形标题字体
        'lines.linewidth': 2,
        'grid.alpha': 0.4,
        'axes.spines.top': True,
        'axes.spines.right': True,
        # ACM论文专用带线衬字体设置
        'font.family': 'serif',
        'font.serif': [
            'Times New Roman', 'Times', 'DejaVu Serif', 'Bitstream Vera Serif',
            'Computer Modern Roman', 'New Century Schoolbook', 'Century Schoolbook L',
            'Utopia', 'ITC Bookman', 'Bookman', 'Nimbus Roman No9 L', 'Palatino',
            'Charter', 'serif'
        ],
        'mathtext.fontset': 'cm',  # Computer Modern数学字体
        'axes.unicode_minus': False
    })

def load_data():
    """加载数据 - 使用全局归一化后的数据"""
    print("加载全局归一化后的数据...")
    data_dir = Path('data')  # 修改路径，因为我们已经在uq_result_analysis目录下

    # 优先加载全局归一化后的数据
    normalized_file = data_dir / 'combined_uq_results_normalized.csv'
    original_file = data_dir / 'combined_uq_results.csv'

    if normalized_file.exists():
        print(f"使用全局归一化数据: {normalized_file}")
        df = pd.read_csv(normalized_file)
    elif original_file.exists():
        print(f"警告: 未找到归一化数据，使用原始数据: {original_file}")
        df = pd.read_csv(original_file)
    else:
        raise FileNotFoundError("未找到数据文件，请先运行数据提取和归一化")

    with open(data_dir / 'data_summary.json', 'r', encoding='utf-8') as f:
        summary = json.load(f)

    print(f"数据加载完成: {len(df)} 条记录")

    # 检查是否包含全局归一化列
    global_norm_columns = [col for col in df.columns if 'global' in col.lower()]
    if global_norm_columns:
        print(f"发现 {len(global_norm_columns)} 个全局归一化列")
        print("主要归一化列:")
        for col in ['uq_score_minmax_global', 'uq_score_zscore_global']:
            if col in df.columns:
                valid_count = df[col].notna().sum()
                print(f"  - {col}: {valid_count} 条有效记录")

    return df, summary

def preprocess_data(df):
    """数据预处理 - 支持全局归一化数据"""
    print("数据预处理...")

    # 移除uq_value为空的记录
    df_clean = df.dropna(subset=['uq_value']).copy()

    # 过滤掉unknown模型
    df_analysis = df_clean[df_clean['llm_model'] != 'unknown'].copy()

    print(f"清理后数据量: {len(df_analysis)}")
    print(f"模型数量: {df_analysis['llm_model'].nunique()}")
    print(f"任务数量: {df_analysis['task_name'].nunique()}")
    print(f"UQ方法数量: {df_analysis['uq_method'].nunique()}")

    # 检查全局归一化列的可用性
    if 'uq_score_minmax_global' in df_analysis.columns:
        valid_global_norm = df_analysis['uq_score_minmax_global'].notna().sum()
        print(f"全局归一化数据: {valid_global_norm} 条记录可用")

        # 如果有全局归一化数据，添加便于使用的列名
        df_analysis['uq_value_global_normalized'] = df_analysis['uq_score_minmax_global']
        df_analysis['uq_value_global_zscore'] = df_analysis['uq_score_zscore_global']
    else:
        print("警告: 未发现全局归一化数据，将使用局部归一化")

    return df_analysis

def normalize_uq_values(df_analysis):
    """对每个UQ方法的值进行归一化处理 - 优先使用全局归一化"""

    # 检查是否已有全局归一化数据
    if 'uq_value_global_normalized' in df_analysis.columns:
        print("使用已有的全局归一化数据...")
        df_normalized = df_analysis.copy()

        # 使用全局归一化数据作为主要归一化列
        df_normalized['uq_value_normalized'] = df_normalized['uq_value_global_normalized']
        df_normalized['uq_value_zscore'] = df_normalized['uq_value_global_zscore']

        # 生成全局归一化统计信息
        normalization_stats = {}
        for method in df_analysis['uq_method'].unique():
            method_data = df_analysis[df_analysis['uq_method'] == method]
            if len(method_data) > 0:
                normalization_stats[method] = {
                    'type': 'global_normalization',
                    'global_min': method_data['global_min'].iloc[0] if 'global_min' in method_data.columns else None,
                    'global_max': method_data['global_max'].iloc[0] if 'global_max' in method_data.columns else None,
                    'global_mean': method_data['global_mean'].iloc[0] if 'global_mean' in method_data.columns else None,
                    'global_std': method_data['global_std'].iloc[0] if 'global_std' in method_data.columns else None,
                    'sample_count': len(method_data)
                }

        print("全局归一化数据使用完成")
        print("归一化方法:")
        print("  - uq_value_normalized: 全局Min-Max归一化到[0,1]")
        print("  - uq_value_zscore: 全局Z-score标准化")

        return df_normalized, normalization_stats

    else:
        # 回退到局部归一化
        print("未发现全局归一化数据，执行局部归一化处理...")

        df_normalized = df_analysis.copy()
        normalization_stats = {}

        for method in df_analysis['uq_method'].unique():
            method_mask = df_analysis['uq_method'] == method
            method_values = df_analysis.loc[method_mask, 'uq_value']

            # 计算归一化参数
            min_val = method_values.min()
            max_val = method_values.max()
            mean_val = method_values.mean()
            std_val = method_values.std()

            # 使用Min-Max归一化到[0,1]
            if max_val != min_val:
                normalized_values = (method_values - min_val) / (max_val - min_val)
            else:
                normalized_values = method_values * 0  # 如果所有值相同，归一化为0

            df_normalized.loc[method_mask, 'uq_value_normalized'] = normalized_values

            # 同时计算Z-score归一化
            if std_val != 0:
                z_score_values = (method_values - mean_val) / std_val
            else:
                z_score_values = method_values * 0

            df_normalized.loc[method_mask, 'uq_value_zscore'] = z_score_values

            # 保存归一化统计信息
            normalization_stats[method] = {
                'type': 'local_normalization',
                'original_min': min_val,
                'original_max': max_val,
                'original_mean': mean_val,
                'original_std': std_val,
                'range': max_val - min_val
            }

        print("局部归一化完成")
        print("归一化方法:")
        print("  - uq_value_normalized: 局部Min-Max归一化到[0,1]")
        print("  - uq_value_zscore: 局部Z-score标准化")

        return df_normalized, normalization_stats

def create_individual_model_task_uq_plots(df_analysis, output_dir):
    """为每个model-task-uq method组合创建单独的分布图（数值分布+核密度）"""
    print("创建model-task-uq method单独分析图...")

    setup_paper_style()

    # 获取所有组合
    combinations = df_analysis.groupby(['llm_model', 'task_name', 'uq_method']).size().reset_index(name='count')
    combinations = combinations[combinations['count'] >= 5]  # 只分析样本数>=5的组合

    print(f"发现 {len(combinations)} 个有效的model-task-uq method组合")

    for idx, row in combinations.iterrows():
        model = row['llm_model']
        task = row['task_name']
        method = row['uq_method']

        # 提取数据
        subset = df_analysis[
            (df_analysis['llm_model'] == model) &
            (df_analysis['task_name'] == task) &
            (df_analysis['uq_method'] == method)
        ]

        if len(subset) < 5:
            continue

        # 创建分布图（直方图 + 核密度估计在同一图上）
        fig, ax1 = plt.subplots(1, 1, figsize=(6, 6))

        # 左y轴：直方图
        ax1.hist(subset['uq_value'], bins=min(20, len(subset)//2), alpha=0.6,
                color='skyblue', edgecolor='black', density=True)
        ax1.set_xlabel('UQ Value')
        ax1.set_ylabel('Histogram Density', color='blue')
        ax1.tick_params(axis='y', labelcolor='blue')
        ax1.grid(True, alpha=0.3)

        # 设置左y轴从0开始
        ax1.set_ylim(bottom=0)

        # 右y轴：核密度估计
        ax2 = ax1.twinx()

        try:
            from scipy.stats import gaussian_kde
            if len(subset['uq_value'].unique()) > 1:  # 确保有足够的变异性
                kde = gaussian_kde(subset['uq_value'])
                # 扩展x范围，确保从数据范围开始
                x_min, x_max = subset['uq_value'].min(), subset['uq_value'].max()
                x_range = np.linspace(x_min, x_max, 200)
                kde_values = kde(x_range)

                # 只绘制线条，不填充
                ax2.plot(x_range, kde_values, 'g-', linewidth=3, alpha=0.8)
            else:
                ax2.text(0.5, 0.5, 'Insufficient variation for KDE',
                        transform=ax2.transAxes, ha='center', va='center', color='green')
        except ImportError:
            # 如果没有scipy，在右轴显示提示
            ax2.text(0.5, 0.5, 'scipy not available for KDE',
                    transform=ax2.transAxes, ha='center', va='center', color='green')

        ax2.set_ylabel('KDE Density', color='green')
        ax2.tick_params(axis='y', labelcolor='green')

        # 设置右y轴从0开始
        ax2.set_ylim(bottom=0)

        # 添加平均值和中位数线（不同颜色和marker）
        mean_val = subset['uq_value'].mean()
        median_val = subset['uq_value'].median()

        # 平均值：红色虚线 + 三角形marker
        ax1.axvline(mean_val, color='red', linestyle='--', linewidth=3, alpha=0.8)
        ax1.scatter([mean_val], [ax1.get_ylim()[1] * 0.9], color='red', marker='^',
                   s=100, zorder=5, label=f'Mean: {mean_val:.3f}')

        # 中位数：橙色点线 + 圆形marker
        ax1.axvline(median_val, color='orange', linestyle=':', linewidth=3, alpha=0.8)
        ax1.scatter([median_val], [ax1.get_ylim()[1] * 0.85], color='orange', marker='o',
                   s=100, zorder=5, label=f'Median: {median_val:.3f}')

        # 不设置标题，保持图表简洁
        # ax1.set_title(f'{model} - {task} - {method}\n')

        # 合并图例并放置在图片底部横向排列
        lines1, labels1 = ax1.get_legend_handles_labels()
        lines2, labels2 = ax2.get_legend_handles_labels()
        ax1.legend(lines1 + lines2, labels1 + labels2,
                  bbox_to_anchor=(0.5, -0.15), loc='upper center',
                  ncol=2, frameon=True, fancybox=True, shadow=True,
                  fontsize=14)  # 稍微调小图例字体以适应横向布局

        plt.tight_layout()
        filename = f"{model}_{task}_{method}_distribution.pdf"
        plt.savefig(output_dir / filename, format='pdf', bbox_inches='tight')
        plt.close()

    print(f"单独分析图已保存到: {output_dir}")

def create_model_task_comparison_plots(df_analysis, output_dir):
    """为每个model-task组合创建UQ方法比较图（只使用归一化后的violin图）"""
    print("创建model-task UQ方法比较图（归一化violin图）...")

    setup_paper_style()

    # 获取model-task组合，且该组合下有多个UQ方法
    model_task_combinations = df_analysis.groupby(['llm_model', 'task_name']).agg({
        'uq_method': 'nunique',
        'uq_value': 'count'
    }).reset_index()
    model_task_combinations.columns = ['llm_model', 'task_name', 'num_methods', 'total_count']

    # 只分析有多个UQ方法且样本数>=50的组合
    model_task_combinations = model_task_combinations[
        (model_task_combinations['num_methods'] > 1) &
        (model_task_combinations['total_count'] >= 50)
    ]

    print(f"发现 {len(model_task_combinations)} 个有效的model-task组合（多UQ方法）")

    for idx, row in model_task_combinations.iterrows():
        model = row['llm_model']
        task = row['task_name']

        # 提取数据
        subset = df_analysis[
            (df_analysis['llm_model'] == model) &
            (df_analysis['task_name'] == task)
        ]

        if len(subset) < 50:
            continue

        methods = subset['uq_method'].unique()
        if len(methods) <= 1:  # 跳过只有一个方法的组合
            continue

        # 只创建归一化值小提琴图
        fig, ax = plt.subplots(1, 1, figsize=(12, 8))
        method_data_norm = [subset[subset['uq_method'] == method]['uq_value_normalized'].values for method in methods]

        try:
            parts = ax.violinplot(method_data_norm, positions=range(len(methods)), showmeans=True, showmedians=True)
            if 'bodies' in parts:
                for pc in parts['bodies']:
                    pc.set_alpha(0.7)
        except Exception as e:
            print(f"Warning: 无法为 {model}-{task} 创建violin图: {e}")
            plt.close()
            continue

        # 不设置标题，保持图表简洁
        # ax.set_title(f'{model} - {task}\nNormalized UQ Values Comparison')
        ax.set_xlabel('UQ Method')
        ax.set_ylabel('Normalized UQ Value [0,1]')
        ax.set_xticks(range(len(methods)))
        ax.set_xticklabels(methods, rotation=45, ha='right')
        ax.grid(True, alpha=0.3)

        plt.tight_layout()
        filename = f"{model}_{task}_normalized_violin.pdf"
        plt.savefig(output_dir / filename, format='pdf', bbox_inches='tight')
        plt.close()

    print(f"归一化方法比较图已保存到: {output_dir}")

# 删除整体分析图 - 不需要

# 删除热力图分析 - 不需要

def create_overall_method_comparison(df_analysis, output_dir):
    """创建整体UQ方法比较图（只保留归一化值小提琴图）"""
    print("创建整体UQ方法比较图（归一化）...")

    setup_paper_style()

    # 计算归一化值的统计
    method_stats_norm = df_analysis.groupby('uq_method')['uq_value_normalized'].agg([
        'count', 'mean', 'std', 'median', 'min', 'max'
    ]).round(4)

    # 创建归一化值小提琴图
    methods = df_analysis['uq_method'].unique()

    fig, ax = plt.subplots(1, 1, figsize=(14, 8))
    method_data_norm = [df_analysis[df_analysis['uq_method'] == method]['uq_value_normalized'].values for method in methods]

    try:
        parts = ax.violinplot(method_data_norm, positions=range(len(methods)), showmeans=True, showmedians=True)
        if 'bodies' in parts:
            for pc in parts['bodies']:
                pc.set_alpha(0.7)
    except Exception as e:
        print(f"Warning: 无法创建整体violin图: {e}")
        plt.close()
        return

    # 不设置标题，保持图表简洁
    # ax.set_title('Overall Normalized UQ Values Distribution [0,1]')
    ax.set_xlabel('UQ Method')
    ax.set_ylabel('Normalized UQ Value')
    ax.set_xticks(range(len(methods)))
    ax.set_xticklabels(methods, rotation=45, ha='right')
    ax.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(output_dir / 'overall_normalized_violin.pdf', format='pdf', bbox_inches='tight')
    plt.close()

    print(f"整体归一化方法比较图已保存到: {output_dir}")

def generate_comprehensive_statistics(df_analysis, output_dir):
    """生成完整的UQ统计信息CSV文件"""
    print("生成完整的UQ统计信息...")

    # 创建完整的统计信息表
    stats_list = []

    # 按模型、任务、方法分组统计
    for (model, task, method), group in df_analysis.groupby(['llm_model', 'task_name', 'uq_method']):
        if len(group) < 5:  # 跳过样本数太少的组合
            continue

        # 原始值统计
        orig_stats = group['uq_value'].describe()

        # 归一化值统计
        norm_stats = group['uq_value_normalized'].describe()

        # Z-score统计
        zscore_stats = group['uq_value_zscore'].describe()

        # 组合统计信息
        stats_row = {
            'llm_model': model,
            'task_name': task,
            'uq_method': method,
            'sample_count': len(group),

            # 原始值统计
            'orig_mean': round(orig_stats['mean'], 4),
            'orig_std': round(orig_stats['std'], 4),
            'orig_var': round(group['uq_value'].var(), 4),
            'orig_median': round(orig_stats['50%'], 4),
            'orig_min': round(orig_stats['min'], 4),
            'orig_max': round(orig_stats['max'], 4),
            'orig_q25': round(orig_stats['25%'], 4),
            'orig_q75': round(orig_stats['75%'], 4),
            'orig_range': round(orig_stats['max'] - orig_stats['min'], 4),
            'orig_cv': round(orig_stats['std'] / orig_stats['mean'], 4) if orig_stats['mean'] != 0 else 0,

            # 归一化值统计
            'norm_mean': round(norm_stats['mean'], 4),
            'norm_std': round(norm_stats['std'], 4),
            'norm_var': round(group['uq_value_normalized'].var(), 4),
            'norm_median': round(norm_stats['50%'], 4),
            'norm_min': round(norm_stats['min'], 4),
            'norm_max': round(norm_stats['max'], 4),
            'norm_q25': round(norm_stats['25%'], 4),
            'norm_q75': round(norm_stats['75%'], 4),
            'norm_range': round(norm_stats['max'] - norm_stats['min'], 4),

            # Z-score统计
            'zscore_mean': round(zscore_stats['mean'], 4),
            'zscore_std': round(zscore_stats['std'], 4),
            'zscore_var': round(group['uq_value_zscore'].var(), 4),
            'zscore_median': round(zscore_stats['50%'], 4),
            'zscore_min': round(zscore_stats['min'], 4),
            'zscore_max': round(zscore_stats['max'], 4),
            'zscore_q25': round(zscore_stats['25%'], 4),
            'zscore_q75': round(zscore_stats['75%'], 4),
            'zscore_range': round(zscore_stats['max'] - zscore_stats['min'], 4),
        }

        stats_list.append(stats_row)

    # 转换为DataFrame并保存
    comprehensive_stats_df = pd.DataFrame(stats_list)
    comprehensive_stats_df = comprehensive_stats_df.sort_values(['llm_model', 'task_name', 'uq_method'])

    # 保存完整统计信息
    comprehensive_stats_df.to_csv(output_dir / 'comprehensive_uq_statistics.csv', index=False)

    print(f"完整UQ统计信息已保存到: {output_dir / 'comprehensive_uq_statistics.csv'}")
    print(f"统计信息包含 {len(comprehensive_stats_df)} 个model-task-method组合")

    return comprehensive_stats_df

# 删除generate_report函数 - 不需要

def main():
    """主函数 - 使用全局归一化数据进行分析"""
    print("=== UQ结果分析开始 ===")
    print("分析流程: 1) 加载全局归一化数据 -> 2) 单独model-task-uq分析 -> 3) model-task合并比较 -> 4) 整体分析")

    # 创建精细化的输出目录结构
    output_dir = Path('data')  # 数据文件保存目录
    figures_base_dir = Path('figures')  # 图片基础目录

    # 创建图片子目录
    individual_plots_dir = figures_base_dir / 'individual_distributions'  # 单个分布图
    comparison_plots_dir = figures_base_dir / 'method_comparisons'        # 方法比较图
    overall_plots_dir = figures_base_dir / 'overall_analysis'             # 整体分析图

    # 确保所有目录存在
    output_dir.mkdir(parents=True, exist_ok=True)
    individual_plots_dir.mkdir(parents=True, exist_ok=True)
    comparison_plots_dir.mkdir(parents=True, exist_ok=True)
    overall_plots_dir.mkdir(parents=True, exist_ok=True)

    print(f"输出目录结构:")
    print(f"  - 数据文件: {output_dir}")
    print(f"  - 单个分布图: {individual_plots_dir}")
    print(f"  - 方法比较图: {comparison_plots_dir}")
    print(f"  - 整体分析图: {overall_plots_dir}")

    # 加载和预处理数据
    df, summary = load_data()
    df_analysis = preprocess_data(df)

    print("\n=== 归一化数据处理 ===")
    # 处理归一化数据（优先使用全局归一化）
    df_analysis, normalization_stats = normalize_uq_values(df_analysis)

    # 保存归一化统计信息
    import json
    with open(output_dir / 'normalization_stats.json', 'w', encoding='utf-8') as f:
        json.dump(normalization_stats, f, indent=2, ensure_ascii=False)
    print(f"归一化统计信息已保存到: {output_dir / 'normalization_stats.json'}")

    print("\n=== 第一步: 单独model-task-uq method分析 ===")
    # 1. 为每个model-task-uq method组合创建单独的分析图
    create_individual_model_task_uq_plots(df_analysis, individual_plots_dir)

    print("\n=== 第二步: model-task合并比较分析（归一化） ===")
    # 2. 为每个model-task组合创建UQ方法比较图（使用归一化值）
    create_model_task_comparison_plots(df_analysis, comparison_plots_dir)

    print("\n=== 第三步: 整体分析（归一化） ===")
    # 只保留整体归一化方法比较
    create_overall_method_comparison(df_analysis, overall_plots_dir)

    print("\n=== 生成统计数据 ===")
    # 生成完整的UQ统计信息CSV文件
    comprehensive_stats = generate_comprehensive_statistics(df_analysis, output_dir)

    # 保存归一化统计信息
    with open(output_dir / 'normalization_stats.json', 'w', encoding='utf-8') as f:
        json.dump(normalization_stats, f, indent=2, ensure_ascii=False)

    # 打印摘要
    print("\n=== 分析完成 ===")
    print(f"总样本数: {len(df_analysis)}")
    print(f"模型数量: {df_analysis['llm_model'].nunique()}")
    print(f"任务数量: {df_analysis['task_name'].nunique()}")
    print(f"UQ方法数量: {df_analysis['uq_method'].nunique()}")
    print(f"\n原始UQ值统计:")
    print(f"  均值: {df_analysis['uq_value'].mean():.4f}")
    print(f"  标准差: {df_analysis['uq_value'].std():.4f}")
    print(f"  范围: [{df_analysis['uq_value'].min():.4f}, {df_analysis['uq_value'].max():.4f}]")

    print(f"\n归一化处理:")
    print(f"  已处理 {len(normalization_stats)} 个UQ方法")

    # 检查归一化类型
    if normalization_stats and list(normalization_stats.values())[0].get('type') == 'global_normalization':
        print(f"  归一化类型: 全局Min-Max [0,1] 和 全局Z-score")
        print(f"  使用全局统计参数进行跨方法一致性归一化")
    else:
        print(f"  归一化类型: 局部Min-Max [0,1] 和 局部Z-score")
        print(f"  使用各方法独立的统计参数进行归一化")

    print(f"\n=== 文件保存总结 ===")
    print(f"统计数据已保存到: {output_dir}")

    # 统计各类图片文件
    individual_pdfs = list(individual_plots_dir.glob("*.pdf"))
    comparison_pdfs = list(comparison_plots_dir.glob("*.pdf"))
    overall_pdfs = list(overall_plots_dir.glob("*.pdf"))

    print(f"\n图表文件分类保存:")
    print(f"  - 单个分布图: {individual_plots_dir} ({len(individual_pdfs)} 个文件)")
    print(f"  - 方法比较图: {comparison_plots_dir} ({len(comparison_pdfs)} 个文件)")
    print(f"  - 整体分析图: {overall_plots_dir} ({len(overall_pdfs)} 个文件)")
    print(f"  - 总计PDF文件: {len(individual_pdfs) + len(comparison_pdfs) + len(overall_pdfs)} 个")

    print(f"\n重要数据文件:")
    print(f"  - 完整UQ统计信息: {output_dir}/comprehensive_uq_statistics.csv")
    print(f"  - 归一化统计信息: {output_dir}/normalization_stats.json")
    print(f"  - 全局归一化摘要: {output_dir}/global_normalization_summary.csv")
    print(f"  - 原始合并数据: {output_dir}/combined_uq_results.csv")
    print(f"  - 归一化合并数据: {output_dir}/combined_uq_results_normalized.csv")

    print(f"\n分析说明:")
    print(f"  - 单个分布图: 每个model-task-UQ方法组合的分布（直方图+核密度）")
    print(f"  - 方法比较图: 每个model-task组合下多个UQ方法的归一化对比（violin图）")
    print(f"  - 整体分析图: 所有UQ方法的全局归一化对比")
    print(f"  - 使用全局归一化确保跨方法的一致性和可比性")

if __name__ == "__main__":
    main()
