# VLLM情感分析系统配置文件

# 模型配置
model:
  name: "Qwen/Qwen3-14B-FP8"  # 使用Qwen3-14B-FP8模型
  base_url: "http://localhost:8000"
  api_key: "token-abc123"
  temperature: 0.7
  top_p: 0.95
  max_tokens: 512
  enable_thinking: true   # 启用thinking模式
  enable_logprobs: true   # 启用logprobs
  top_logprobs: 1         # 返回top-1的logprobs
  stream: false           # 禁用流处理

# 输出配置
output:
  format: "json"  # json, csv, mongodb
  # 文件输出设置
  file:
    output_dir: "output"
    filename_prefix: "vllm_sentiment_responses"

# 日志配置
logging:
  level: "INFO"
  file: "vllm_sentiment_generator.log"

# 任务配置
tasks:
  sentiment_analysis:
    enabled: true  # 启用情感分析任务
    name: "sentiment_analysis"
    task_category: "sentiment_analysis"
    dataset_source: "twitter_sentiment"
    data_file: "sampled_semeval.csv"
    prompt_dir: "prompts/1_sentiment_analysis"
    num_prompts: 10
    sample_prompts: 5  # 从10个中随机选择5个
    attempts_per_prompt: 6  # 每个prompt重复6次
    template_variable: "tweet"  # prompt中的占位符
    id_field: "id"
    text_field: "text"
    label_field: "label"
    max_samples: null  # 处理所有样本

# 系统配置
system:
  max_concurrent_requests: 1
  max_retries: 3
  retry_delay: 1.0
  enable_resume: true
  test_mode_sample_size: 5
