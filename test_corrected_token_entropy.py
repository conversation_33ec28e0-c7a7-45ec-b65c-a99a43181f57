#!/usr/bin/env python3
"""
Test corrected token entropy UQ methods.
Validates the updated implementation that:
1. Requires logprobs data (no fallback)
2. Uses correct algorithm: mean probabilities -> entropy
"""

import numpy as np
import json
from uq_methods.implementations.token_entropy import MeanTokenEntropyUQ, TokenEntropyUQ


def create_sample_logprobs_data():
    """Create sample logprobs data for testing."""
    # Sample response 1: High certainty tokens
    sample_1 = {
        "logprobs": {
            "content": [
                {
                    "token": "The",
                    "top_logprobs": [
                        {"token": "The", "logprob": -0.1},  # High prob: exp(-0.1) ≈ 0.905
                        {"token": "A", "logprob": -2.3},    # Low prob: exp(-2.3) ≈ 0.100
                    ]
                },
                {
                    "token": "answer",
                    "top_logprobs": [
                        {"token": "answer", "logprob": -0.2},  # High prob: exp(-0.2) ≈ 0.819
                        {"token": "solution", "logprob": -2.5},
                    ]
                },
                {
                    "token": "is",
                    "top_logprobs": [
                        {"token": "is", "logprob": -0.05},  # Very high prob: exp(-0.05) ≈ 0.951
                        {"token": "was", "logprob": -3.0},
                    ]
                }
            ]
        }
    }
    
    # Sample response 2: Lower certainty tokens
    sample_2 = {
        "logprobs": {
            "content": [
                {
                    "token": "I",
                    "top_logprobs": [
                        {"token": "I", "logprob": -0.8},    # Lower prob: exp(-0.8) ≈ 0.449
                        {"token": "We", "logprob": -0.9},
                    ]
                },
                {
                    "token": "think",
                    "top_logprobs": [
                        {"token": "think", "logprob": -1.1}, # Lower prob: exp(-1.1) ≈ 0.333
                        {"token": "believe", "logprob": -1.0},
                    ]
                }
            ]
        }
    }
    
    # Sample response 3: Medium certainty tokens
    sample_3 = {
        "logprobs": {
            "content": [
                {
                    "token": "Maybe",
                    "top_logprobs": [
                        {"token": "Maybe", "logprob": -0.5},  # Medium prob: exp(-0.5) ≈ 0.607
                        {"token": "Perhaps", "logprob": -1.2},
                    ]
                },
                {
                    "token": "yes",
                    "top_logprobs": [
                        {"token": "yes", "logprob": -0.6},   # Medium prob: exp(-0.6) ≈ 0.549
                        {"token": "no", "logprob": -1.0},
                    ]
                }
            ]
        }
    }
    
    return [sample_1, sample_2, sample_3]


def test_mean_token_entropy_algorithm():
    """Test that MeanTokenEntropyUQ follows the correct algorithm."""
    print("=== Testing MeanTokenEntropyUQ Algorithm ===")
    
    responses = ["The answer is", "I think", "Maybe yes"]
    responses_with_probs = create_sample_logprobs_data()
    
    method = MeanTokenEntropyUQ(verbose=True)
    result = method.compute_uncertainty(responses, responses_with_probs)
    
    print(f"Result: {json.dumps(result, indent=2, default=str)}")
    
    # Manual calculation to verify:
    # Response 1 tokens: exp(-0.1)≈0.905, exp(-0.2)≈0.819, exp(-0.05)≈0.951
    # Response 1 mean prob: (0.905 + 0.819 + 0.951) / 3 ≈ 0.892
    
    # Response 2 tokens: exp(-0.8)≈0.449, exp(-1.1)≈0.333  
    # Response 2 mean prob: (0.449 + 0.333) / 2 ≈ 0.391
    
    # Response 3 tokens: exp(-0.5)≈0.607, exp(-0.6)≈0.549
    # Response 3 mean prob: (0.607 + 0.549) / 2 ≈ 0.578
    
    expected_mean_probs = [0.892, 0.391, 0.578]  # Approximate
    
    if 'response_mean_probs' in result:
        actual_probs = result['response_mean_probs']
        print(f"\nExpected mean probs (approx): {[f'{p:.3f}' for p in expected_mean_probs]}")
        print(f"Actual mean probs: {[f'{p:.3f}' for p in actual_probs]}")
        
        # Calculate expected entropy manually
        total = sum(expected_mean_probs)
        normalized = [p/total for p in expected_mean_probs]
        expected_entropy = -sum(p * np.log(p) for p in normalized if p > 0)
        print(f"\nExpected entropy (manual): {expected_entropy:.4f}")
        print(f"Actual entropy: {result['uncertainty_score']:.4f}")
    
    return result


def test_no_logprobs_error():
    """Test that methods return error when no logprobs provided."""
    print("\n\n=== Testing No Logprobs Error ===")
    
    responses = ["The answer is correct", "I think maybe yes"]
    
    # Test MeanTokenEntropy
    mean_method = MeanTokenEntropyUQ(verbose=False)
    mean_result = mean_method.compute_uncertainty(responses)  # No logprobs
    
    print(f"MeanTokenEntropy without logprobs:")
    print(f"  Error: {mean_result.get('error', 'No error')}")
    print(f"  Uncertainty Score: {mean_result['uncertainty_score']}")
    
    # Test TokenEntropy
    token_method = TokenEntropyUQ(verbose=False)
    token_result = token_method.compute_uncertainty(responses)  # No logprobs
    
    print(f"\nTokenEntropy without logprobs:")
    print(f"  Error: {token_result.get('error', 'No error')}")
    print(f"  Uncertainty Score: {token_result['uncertainty_score']}")
    
    return mean_result, token_result


def test_different_response_certainties():
    """Test with responses of different certainty levels."""
    print("\n\n=== Testing Different Response Certainties ===")
    
    # Create responses with very different certainty levels
    high_certainty = {
        "logprobs": {
            "content": [
                {"token": "Yes", "top_logprobs": [{"token": "Yes", "logprob": -0.01}]},  # Very certain
                {"token": "definitely", "top_logprobs": [{"token": "definitely", "logprob": -0.02}]},
            ]
        }
    }
    
    medium_certainty = {
        "logprobs": {
            "content": [
                {"token": "Maybe", "top_logprobs": [{"token": "Maybe", "logprob": -0.7}]},  # Medium
                {"token": "possibly", "top_logprobs": [{"token": "possibly", "logprob": -0.8}]},
            ]
        }
    }
    
    low_certainty = {
        "logprobs": {
            "content": [
                {"token": "Unsure", "top_logprobs": [{"token": "Unsure", "logprob": -2.0}]},  # Low
                {"token": "uncertain", "top_logprobs": [{"token": "uncertain", "logprob": -2.5}]},
            ]
        }
    }
    
    responses = ["Yes definitely", "Maybe possibly", "Unsure uncertain"]
    responses_with_probs = [high_certainty, medium_certainty, low_certainty]
    
    method = MeanTokenEntropyUQ(verbose=True)
    result = method.compute_uncertainty(responses, responses_with_probs)
    
    print(f"High vs Medium vs Low certainty responses:")
    print(f"Mean probabilities: {[f'{p:.4f}' for p in result.get('response_mean_probs', [])]}")
    print(f"Uncertainty Score: {result['uncertainty_score']:.4f}")
    print(f"Expected: High uncertainty due to very different certainty levels")
    
    return result


def test_similar_responses():
    """Test with very similar responses (should have low uncertainty)."""
    print("\n\n=== Testing Similar Responses ===")
    
    # All responses have similar high certainty
    similar_response_1 = {
        "logprobs": {
            "content": [
                {"token": "Yes", "top_logprobs": [{"token": "Yes", "logprob": -0.1}]},
                {"token": "correct", "top_logprobs": [{"token": "correct", "logprob": -0.12}]},
            ]
        }
    }
    
    similar_response_2 = {
        "logprobs": {
            "content": [
                {"token": "Yes", "top_logprobs": [{"token": "Yes", "logprob": -0.11}]},
                {"token": "right", "top_logprobs": [{"token": "right", "logprob": -0.13}]},
            ]
        }
    }
    
    similar_response_3 = {
        "logprobs": {
            "content": [
                {"token": "Yes", "top_logprobs": [{"token": "Yes", "logprob": -0.09}]},
                {"token": "accurate", "top_logprobs": [{"token": "accurate", "logprob": -0.14}]},
            ]
        }
    }
    
    responses = ["Yes correct", "Yes right", "Yes accurate"]
    responses_with_probs = [similar_response_1, similar_response_2, similar_response_3]
    
    method = MeanTokenEntropyUQ(verbose=True)
    result = method.compute_uncertainty(responses, responses_with_probs)
    
    print(f"Similar high-certainty responses:")
    print(f"Mean probabilities: {[f'{p:.4f}' for p in result.get('response_mean_probs', [])]}")
    print(f"Uncertainty Score: {result['uncertainty_score']:.4f}")
    print(f"Expected: Low uncertainty due to similar certainty levels")
    
    return result


if __name__ == "__main__":
    print("Testing Corrected Token Entropy UQ Methods")
    print("=" * 60)
    
    try:
        # Test the corrected algorithm
        algorithm_result = test_mean_token_entropy_algorithm()
        
        # Test error handling
        error_results = test_no_logprobs_error()
        
        # Test different scenarios
        different_certainties = test_different_response_certainties()
        similar_responses = test_similar_responses()
        
        print("\n" + "=" * 60)
        print("All tests completed successfully! ✅")
        
        print(f"\nKey Validation Points:")
        print(f"✅ No fallback mechanism - requires logprobs")
        print(f"✅ Correct algorithm: mean token probs -> entropy")
        print(f"✅ Higher uncertainty for diverse certainty levels")
        print(f"✅ Lower uncertainty for similar certainty levels")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        raise
