# Token Entropy UQ Methods

本文档介绍新增的基于token级别概率信息的不确定性量化方法。

## 🎯 概述

新增了两个基于token概率分布的不确定性量化方法：

1. **MeanTokenEntropyUQ** - 序列级不确定性度量
2. **TokenEntropyUQ** - token级不确定性度量

这两个方法利用语言模型生成时的token概率信息（logprobs）来计算不确定性，适用于支持logprobs输出的模型。

## 📊 方法说明

### MeanTokenEntropyUQ
- **功能**: 计算序列中所有token的平均熵，提供序列级不确定性度量
- **输入**: 需要logprobs数据或普通响应文本
- **输出**: 单一的不确定性分数，表示整个序列的平均不确定性
- **适用场景**: 当需要评估整体响应的不确定性时

**特点**:
- 支持VLLM格式的logprobs数据
- 支持多种logprobs数据格式
- 当没有概率数据时提供fallback机制
- 返回详细的统计信息（均值、标准差、最小值、最大值）

### TokenEntropyUQ
- **功能**: 计算每个token的熵值，提供细粒度的不确定性分析
- **输入**: 必须有logprobs数据
- **输出**: 每个响应的token级熵值数组
- **适用场景**: 需要分析具体哪些token或位置不确定性较高时

**特点**:
- 返回每个token的详细熵值
- 提供熵值的统计分析（均值、标准差、中位数等）
- 支持跨多个响应的token分析

## 🔧 使用方法

### 1. 基本用法

```python
from uq_methods.implementations.token_entropy import MeanTokenEntropyUQ, TokenEntropyUQ

# 初始化方法
mean_entropy = MeanTokenEntropyUQ(verbose=True)
token_entropy = TokenEntropyUQ(verbose=True)

# 准备数据（包含logprobs的响应）
responses = ["The answer is correct"]
responses_with_probs = [{
    "logprobs": {
        "content": [
            {
                "token": "The",
                "top_logprobs": [
                    {"token": "The", "logprob": -0.1},
                    {"token": "A", "logprob": -2.3}
                ]
            },
            # ... 更多token
        ]
    }
}]

# 计算不确定性
mean_result = mean_entropy.compute_uncertainty(responses, responses_with_probs)
token_result = token_entropy.compute_uncertainty(responses, responses_with_probs)

print(f"Mean Token Entropy: {mean_result['uncertainty_score']:.4f}")
print(f"Token-level Entropies: {token_result['token_entropies']}")
```

### 2. 在配置文件中使用

在实验配置文件中添加：

```yaml
enabled_methods:
  - "MeanTokenEntropyUQ"
  - "TokenEntropyUQ"
  
method_params:
  MeanTokenEntropyUQ:
    verbose: true
  TokenEntropyUQ:
    verbose: false
```

### 3. VLLM集成示例

```python
# 与VLLM情感分析生成器集成
from vllm_sentiment_generator import VLLMSentimentGenerator

generator = VLLMSentimentGenerator()
result = generator.analyze_sentiment_with_logprobs("I love this product!")

# 准备UQ分析数据
responses_with_probs = [{"logprobs": result["logprobs"]}]
responses = [result["raw_response"]]

# 分析不确定性
uncertainty_result = mean_entropy.compute_uncertainty(responses, responses_with_probs)
```

## 📋 数据格式

### 支持的Logprobs格式

1. **VLLM格式**:
```json
{
  "logprobs": {
    "content": [
      {
        "token": "positive",
        "top_logprobs": [
          {"token": "positive", "logprob": -0.1625},
          {"token": "negative", "logprob": -2.1203}
        ]
      }
    ]
  }
}
```

2. **简化格式**:
```json
{
  "logprobs": {
    "tokens": ["positive", "sentiment"],
    "top_logprobs": [
      {"positive": 0.85, "negative": 0.12, "neutral": 0.03},
      {"sentiment": 0.92, "feeling": 0.05, "emotion": 0.03}
    ]
  }
}
```

## 🎲 输出示例

### MeanTokenEntropyUQ输出
```json
{
  "uncertainty_score": 0.7887,
  "mean_token_entropy": 0.7887,
  "entropy_std": 0.2400,
  "entropy_min": 0.5048,
  "entropy_max": 1.0917,
  "num_responses": 3,
  "num_valid_responses": 3,
  "total_tokens": 11,
  "method": "MeanTokenEntropy"
}
```

### TokenEntropyUQ输出
```json
{
  "uncertainty_score": 0.7336,
  "token_entropies": [
    [0.488, 0.470, 0.278, 0.523],
    [1.095, 0.902, 0.681],
    [0.770, 0.615, 0.925]
  ],
  "total_tokens": 10,
  "method": "TokenEntropy",
  "metadata": {
    "entropy_statistics": {
      "mean": 0.7336,
      "std": 0.3027,
      "min": 0.2777,
      "max": 1.0953,
      "median": 0.6808
    }
  }
}
```

## 🔍 技术细节

### 熵计算公式
对于每个token位置的概率分布 `P = {p₁, p₂, ..., pₙ}`，熵计算为：

```
H(P) = -∑(pᵢ × log(pᵢ))
```

### 方法特性
- **MeanTokenEntropyUQ**: 计算所有token熵的平均值
- **TokenEntropyUQ**: 返回每个token的熵值（排除最后一个token，遵循参考实现）
- **概率归一化**: 自动处理概率分布的归一化
- **错误处理**: 完善的异常处理和日志记录

## 🚀 系统集成

这些方法已经自动集成到UQ方法发现系统中：

```bash
# 查看所有可用方法
python discover_uq_methods.py

# 输出将包含：
# 1. MeanTokenEntropyUQ
# 2. TokenEntropyUQ
```

可以在任何使用UQ方法加载器的地方直接使用：

```python
from uq_analysis.method_loader import create_method_loader

loader = create_method_loader()
mean_entropy = loader.load_method("MeanTokenEntropyUQ", {"verbose": True})
token_entropy = loader.load_method("TokenEntropyUQ")
```

## 📝 注意事项

1. **TokenEntropyUQ**必须有logprobs数据才能工作
2. **MeanTokenEntropyUQ**在没有logprobs时会使用简单的响应多样性作为fallback
3. 支持多种logprobs数据格式，会自动检测并适配
4. 所有概率值都会自动归一化以确保正确的熵计算
5. 遵循项目的记忆设置，不会生成不必要的嵌入向量[[memory:2827797]]

## 🎯 应用场景

- **模型置信度评估**: 了解模型对特定生成内容的确信程度
- **质量控制**: 识别高不确定性的输出进行人工审核
- **主动学习**: 选择不确定性高的样本进行标注
- **A/B测试**: 比较不同模型配置的不确定性表现
- **实时监控**: 在生产环境中监控模型输出的不确定性趋势
