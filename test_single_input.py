#!/usr/bin/env python3
"""
测试VLLM情感分析生成器 - 生成第一个输入的回复到测试collection
"""

import os
import sys
import logging
from typing import Dict, Any, List
import pandas as pd

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_test_config() -> Dict[str, Any]:
    """创建测试配置"""
    return {
        'model': {
            'name': 'Qwen/Qwen3-14B-FP8',
            'base_url': 'http://localhost:8000',
            'api_key': 'token-abc123',
            'temperature': 0.7,
            'top_p': 0.95,
            'max_tokens': 1500,
            'enable_thinking': True,
            'enable_logprobs': True,
            'top_logprobs': 0,
            'stream': False
        },
        'output': {
            'format': 'mongodb',
            'mongo': {
                'host': 'localhost',
                'port': 27017,
                'database': 'LLM-UQ',
                'collection': 'test_response_collection'  # 使用测试collection
            }
        },
        'tasks': {
            'sentiment_analysis': {
                'enabled': True,
                'name': 'sentiment_analysis',
                'task_category': 'sentiment_analysis',
                'dataset_source': 'twitter_sentiment',
                'sample_prompts': 5,
                'attempts_per_prompt': 6,
                'template_variable': 'tweet',
                'id_field': 'id',
                'text_field': 'text',
                'label_field': 'label',
                'data_file': 'sampled_semeval.csv',
                'max_samples': 1  # 只处理第一个样本
            }
        }
    }

def main():
    """主函数"""
    logger.info("开始测试VLLM情感分析生成器 - 生成第一个输入的回复")
    
    try:
        # 导入VLLM生成器
        from vllm_sentiment_generator import VLLMSentimentGenerator
        
        # 创建生成器，使用测试配置
        generator = VLLMSentimentGenerator(
            vllm_host="http://localhost:8000",
            api_key="token-abc123",
            model_name="Qwen/Qwen3-14B-FP8",
            config_path=None
        )
        
        # 设置测试配置
        generator.config = create_test_config()
        
        # 重新初始化MongoDB连接到测试collection
        generator._init_mongodb()
        
        logger.info(f"MongoDB连接状态: {generator.collection is not None}")
        if generator.collection is not None:
            logger.info(f"使用collection: {generator.collection.name}")
        
        # 检查数据文件是否存在
        data_file = 'sampled_semeval.csv'
        if not os.path.exists(data_file):
            logger.error(f"数据文件不存在: {data_file}")
            return 1
        
        # 加载第一个数据项
        df = pd.read_csv(data_file)
        if len(df) == 0:
            logger.error("数据文件为空")
            return 1
        
        first_record = df.iloc[0].to_dict()
        logger.info(f"第一个数据项: ID={first_record.get('id', 'unknown')}, Text='{first_record.get('text', '')[:100]}...'")
        
        # 创建数据列表（只包含第一个记录）
        data = [first_record]
        
        # 使用原有逻辑处理数据
        run_id = generator.process_semeval_data_like_original(data, run_id=None)
        
        logger.info(f"处理完成！")
        logger.info(f"Run ID: {run_id}")
        logger.info(f"数据已保存到MongoDB collection: {generator.collection.name}")
        logger.info(f"数据库: {generator.db.name}")
        
        # 查询刚刚插入的文档数量
        if generator.collection is not None:
            count = generator.collection.count_documents({"run_id": run_id})
            logger.info(f"插入的文档数量: {count}")

            # 显示第一个文档的关键信息
            first_doc = generator.collection.find_one({"run_id": run_id})
            if first_doc:
                logger.info("第一个文档的关键信息:")
                logger.info(f"  - task_id: {first_doc.get('task_id')}")
                logger.info(f"  - prompt_seed: {first_doc.get('prompt_seed')}")
                logger.info(f"  - prompt_index: {first_doc.get('prompt_index')}")
                logger.info(f"  - task_attempt_prompt: {first_doc.get('task_attempt_prompt')}")
                logger.info(f"  - parsed_answer: {first_doc.get('parsed_answer')}")
                logger.info(f"  - confidence: {first_doc.get('confidence')}")
        
        return 0
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
